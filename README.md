# BOSS直聘职位URL爬虫

基于Position_Crawler_1项目核心技术的BOSS直聘职位URL爬虫程序。

## 项目结构

```
Position_url_crawler/
├── main.py                    # 主程序入口
├── position_crawler_core.py   # 核心爬虫逻辑
├── advanced_stealth.py        # 高级反检测技术
├── smart_retry.py            # 智能重试机制
├── crawler_config.py         # 配置管理
├── crawler_config.json       # 配置文件
├── url_validator.py          # URL验证工具
├── data_analyzer.py          # 数据分析工具
└── requirements.txt          # 项目依赖
```

## 核心功能

- **高级反检测**: 集成Position_Crawler_1的反爬虫检测技术
- **智能重试**: 自适应重试策略，提高爬取成功率
- **URL验证**: 确保爬取的URL有效性
- **数据分析**: 对爬取结果进行统计分析
- **配置管理**: 灵活的配置系统

## 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 配置参数
编辑 `crawler_config.json` 文件，设置搜索关键词和其他参数。

### 3. 运行爬虫
```bash
python main.py
```

### 4. 查看结果
爬取的职位URL将保存为txt格式文件。

## 配置说明

主要配置项：
- `search_keywords`: 搜索关键词列表
- `max_pages`: 最大爬取页数
- `delay_range`: 请求间隔时间范围
- `output_format`: 输出格式（默认txt）

## 技术特点

- 基于Position_Crawler_1项目的成熟技术架构
- 专门针对BOSS直聘的反爬虫机制优化
- 高成功率的职位URL提取
- 轻量级设计，专注核心功能

## 注意事项

- 请遵守网站使用条款
- 建议合理设置爬取频率
- 仅用于学习和研究目的
