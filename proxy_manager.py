#!/usr/bin/env python3
"""
代理管理模块
管理代理IP池，实现代理轮换和健康检查
"""

import asyncio
import aiohttp
import time
import random
import logging
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import json

logger = logging.getLogger(__name__)

class ProxyStatus(Enum):
    """代理状态枚举"""
    UNKNOWN = "unknown"
    ACTIVE = "active"
    FAILED = "failed"
    BANNED = "banned"
    TIMEOUT = "timeout"

@dataclass
class ProxyInfo:
    """代理信息"""
    host: str
    port: int
    username: Optional[str] = None
    password: Optional[str] = None
    protocol: str = "http"
    status: ProxyStatus = ProxyStatus.UNKNOWN
    response_time: float = 0.0
    success_count: int = 0
    failure_count: int = 0
    last_used: float = 0.0
    last_checked: float = 0.0
    
    @property
    def url(self) -> str:
        """获取代理URL"""
        if self.username and self.password:
            return f"{self.protocol}://{self.username}:{self.password}@{self.host}:{self.port}"
        else:
            return f"{self.protocol}://{self.host}:{self.port}"
            
    @property
    def success_rate(self) -> float:
        """成功率"""
        total = self.success_count + self.failure_count
        return self.success_count / total if total > 0 else 0.0
        
    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            "host": self.host,
            "port": self.port,
            "username": self.username,
            "password": self.password,
            "protocol": self.protocol,
            "status": self.status.value,
            "response_time": self.response_time,
            "success_count": self.success_count,
            "failure_count": self.failure_count,
            "success_rate": self.success_rate,
            "last_used": self.last_used,
            "last_checked": self.last_checked
        }

class ProxyManager:
    """代理管理器"""
    
    def __init__(self, check_interval: int = 300, max_failures: int = 5):
        self.proxies: List[ProxyInfo] = []
        self.check_interval = check_interval  # 健康检查间隔（秒）
        self.max_failures = max_failures  # 最大失败次数
        self.current_index = 0
        self.is_checking = False
        self.check_task: Optional[asyncio.Task] = None
        
    def add_proxy(self, host: str, port: int, username: str = None, 
                  password: str = None, protocol: str = "http") -> None:
        """添加代理"""
        proxy = ProxyInfo(
            host=host,
            port=port,
            username=username,
            password=password,
            protocol=protocol
        )
        self.proxies.append(proxy)
        logger.info(f"✅ 添加代理: {host}:{port}")
        
    def load_proxies_from_file(self, filepath: str) -> int:
        """从文件加载代理列表"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                
            count = 0
            for line in lines:
                line = line.strip()
                if not line or line.startswith('#'):
                    continue
                    
                # 支持多种格式
                # host:port
                # host:port:username:password
                # protocol://host:port
                # protocol://username:password@host:port
                
                if '://' in line:
                    # URL格式
                    try:
                        from urllib.parse import urlparse
                        parsed = urlparse(line)
                        self.add_proxy(
                            host=parsed.hostname,
                            port=parsed.port,
                            username=parsed.username,
                            password=parsed.password,
                            protocol=parsed.scheme
                        )
                        count += 1
                    except:
                        logger.warning(f"⚠️ 无效的代理URL: {line}")
                else:
                    # 简单格式
                    parts = line.split(':')
                    if len(parts) >= 2:
                        try:
                            host = parts[0]
                            port = int(parts[1])
                            username = parts[2] if len(parts) > 2 else None
                            password = parts[3] if len(parts) > 3 else None
                            
                            self.add_proxy(host, port, username, password)
                            count += 1
                        except:
                            logger.warning(f"⚠️ 无效的代理格式: {line}")
                            
            logger.info(f"📋 从文件加载了 {count} 个代理")
            return count
            
        except Exception as e:
            logger.error(f"❌ 加载代理文件失败: {e}")
            return 0
            
    def get_next_proxy(self) -> Optional[ProxyInfo]:
        """获取下一个可用代理"""
        if not self.proxies:
            return None
            
        # 过滤可用代理
        available_proxies = [
            p for p in self.proxies 
            if p.status in [ProxyStatus.UNKNOWN, ProxyStatus.ACTIVE] 
            and p.failure_count < self.max_failures
        ]
        
        if not available_proxies:
            logger.warning("⚠️ 没有可用的代理")
            return None
            
        # 按成功率和响应时间排序
        available_proxies.sort(key=lambda p: (-p.success_rate, p.response_time))
        
        # 选择最佳代理
        proxy = available_proxies[0]
        proxy.last_used = time.time()
        
        return proxy
        
    def get_random_proxy(self) -> Optional[ProxyInfo]:
        """获取随机代理"""
        available_proxies = [
            p for p in self.proxies 
            if p.status in [ProxyStatus.UNKNOWN, ProxyStatus.ACTIVE]
            and p.failure_count < self.max_failures
        ]
        
        if not available_proxies:
            return None
            
        proxy = random.choice(available_proxies)
        proxy.last_used = time.time()
        return proxy
        
    def mark_proxy_success(self, proxy: ProxyInfo, response_time: float = 0.0) -> None:
        """标记代理成功"""
        proxy.status = ProxyStatus.ACTIVE
        proxy.success_count += 1
        proxy.response_time = response_time
        logger.debug(f"✅ 代理成功: {proxy.host}:{proxy.port}")
        
    def mark_proxy_failure(self, proxy: ProxyInfo, reason: str = "") -> None:
        """标记代理失败"""
        proxy.failure_count += 1
        
        if proxy.failure_count >= self.max_failures:
            proxy.status = ProxyStatus.FAILED
            logger.warning(f"❌ 代理失效: {proxy.host}:{proxy.port} (失败{proxy.failure_count}次)")
        else:
            logger.debug(f"⚠️ 代理失败: {proxy.host}:{proxy.port} - {reason}")
            
    async def check_proxy(self, proxy: ProxyInfo, test_url: str = "http://httpbin.org/ip") -> bool:
        """检查单个代理"""
        start_time = time.time()
        
        try:
            connector = aiohttp.TCPConnector()
            timeout = aiohttp.ClientTimeout(total=10)
            
            async with aiohttp.ClientSession(
                connector=connector,
                timeout=timeout
            ) as session:
                async with session.get(
                    test_url,
                    proxy=proxy.url
                ) as response:
                    if response.status == 200:
                        response_time = time.time() - start_time
                        self.mark_proxy_success(proxy, response_time)
                        proxy.last_checked = time.time()
                        return True
                    else:
                        self.mark_proxy_failure(proxy, f"HTTP {response.status}")
                        return False
                        
        except asyncio.TimeoutError:
            proxy.status = ProxyStatus.TIMEOUT
            self.mark_proxy_failure(proxy, "超时")
            return False
        except Exception as e:
            self.mark_proxy_failure(proxy, str(e))
            return False
            
    async def check_all_proxies(self, test_url: str = "http://httpbin.org/ip") -> Dict[str, int]:
        """检查所有代理"""
        if not self.proxies:
            return {"total": 0, "active": 0, "failed": 0}
            
        logger.info(f"🔍 开始检查 {len(self.proxies)} 个代理...")
        
        # 并发检查所有代理
        tasks = [self.check_proxy(proxy, test_url) for proxy in self.proxies]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 统计结果
        active_count = sum(1 for r in results if r is True)
        failed_count = len(results) - active_count
        
        logger.info(f"✅ 代理检查完成: {active_count} 个可用, {failed_count} 个失效")
        
        return {
            "total": len(self.proxies),
            "active": active_count,
            "failed": failed_count
        }
        
    def start_health_check(self, test_url: str = "http://httpbin.org/ip") -> None:
        """启动健康检查"""
        if self.is_checking:
            logger.warning("健康检查已在运行")
            return
            
        self.is_checking = True
        self.check_task = asyncio.create_task(self._health_check_loop(test_url))
        logger.info("🔍 代理健康检查已启动")
        
    def stop_health_check(self) -> None:
        """停止健康检查"""
        self.is_checking = False
        if self.check_task:
            self.check_task.cancel()
        logger.info("⏹️ 代理健康检查已停止")
        
    async def _health_check_loop(self, test_url: str) -> None:
        """健康检查循环"""
        while self.is_checking:
            try:
                await self.check_all_proxies(test_url)
                await asyncio.sleep(self.check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"健康检查异常: {e}")
                await asyncio.sleep(60)  # 出错时等待1分钟
                
    def get_proxy_stats(self) -> Dict[str, any]:
        """获取代理统计信息"""
        if not self.proxies:
            return {"total": 0}
            
        status_counts = {}
        for proxy in self.proxies:
            status = proxy.status.value
            status_counts[status] = status_counts.get(status, 0) + 1
            
        active_proxies = [p for p in self.proxies if p.status == ProxyStatus.ACTIVE]
        
        stats = {
            "total": len(self.proxies),
            "status_distribution": status_counts,
            "active_count": len(active_proxies),
            "average_response_time": 0.0,
            "best_proxy": None,
            "worst_proxy": None
        }
        
        if active_proxies:
            # 平均响应时间
            total_time = sum(p.response_time for p in active_proxies)
            stats["average_response_time"] = total_time / len(active_proxies)
            
            # 最佳和最差代理
            best_proxy = min(active_proxies, key=lambda p: p.response_time)
            worst_proxy = max(active_proxies, key=lambda p: p.response_time)
            
            stats["best_proxy"] = {
                "host": best_proxy.host,
                "port": best_proxy.port,
                "response_time": best_proxy.response_time,
                "success_rate": best_proxy.success_rate
            }
            
            stats["worst_proxy"] = {
                "host": worst_proxy.host,
                "port": worst_proxy.port,
                "response_time": worst_proxy.response_time,
                "success_rate": worst_proxy.success_rate
            }
            
        return stats
        
    def generate_report(self) -> str:
        """生成代理报告"""
        stats = self.get_proxy_stats()
        
        report_lines = []
        report_lines.append("=" * 80)
        report_lines.append("🌐 代理管理报告")
        report_lines.append("=" * 80)
        report_lines.append(f"生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append("")
        
        # 基础统计
        report_lines.append("📊 基础统计")
        report_lines.append("-" * 40)
        report_lines.append(f"代理总数: {stats['total']}")
        report_lines.append(f"可用代理: {stats['active_count']}")
        
        if stats.get('status_distribution'):
            report_lines.append("状态分布:")
            for status, count in stats['status_distribution'].items():
                report_lines.append(f"  - {status}: {count}")
                
        report_lines.append("")
        
        # 性能统计
        if stats['active_count'] > 0:
            report_lines.append("⚡ 性能统计")
            report_lines.append("-" * 40)
            report_lines.append(f"平均响应时间: {stats['average_response_time']:.2f}秒")
            
            if stats.get('best_proxy'):
                best = stats['best_proxy']
                report_lines.append(f"最佳代理: {best['host']}:{best['port']} ({best['response_time']:.2f}秒)")
                
            if stats.get('worst_proxy'):
                worst = stats['worst_proxy']
                report_lines.append(f"最慢代理: {worst['host']}:{worst['port']} ({worst['response_time']:.2f}秒)")
                
            report_lines.append("")
            
        # 代理列表
        if self.proxies:
            report_lines.append("📋 代理列表")
            report_lines.append("-" * 40)
            for i, proxy in enumerate(self.proxies[:10], 1):  # 只显示前10个
                status_icon = {
                    ProxyStatus.ACTIVE: "✅",
                    ProxyStatus.FAILED: "❌",
                    ProxyStatus.TIMEOUT: "⏰",
                    ProxyStatus.BANNED: "🚫",
                    ProxyStatus.UNKNOWN: "❓"
                }.get(proxy.status, "❓")
                
                report_lines.append(
                    f"{i:2d}. {status_icon} {proxy.host}:{proxy.port} "
                    f"({proxy.response_time:.2f}s, {proxy.success_rate:.1%})"
                )
                
            if len(self.proxies) > 10:
                report_lines.append(f"... 还有 {len(self.proxies) - 10} 个代理")
                
        report_lines.append("")
        report_lines.append("=" * 80)
        
        return "\n".join(report_lines)
        
    def save_proxy_data(self, filename: str = None) -> str:
        """保存代理数据"""
        if not filename:
            timestamp = time.strftime('%Y%m%d_%H%M%S')
            filename = f"proxy_data_{timestamp}.json"
            
        try:
            data = {
                "metadata": {
                    "generated_at": time.strftime('%Y-%m-%d %H:%M:%S'),
                    "total_proxies": len(self.proxies),
                    "check_interval": self.check_interval,
                    "max_failures": self.max_failures
                },
                "statistics": self.get_proxy_stats(),
                "proxies": [proxy.to_dict() for proxy in self.proxies]
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
            logger.info(f"💾 代理数据已保存到: {filename}")
            return filename
            
        except Exception as e:
            logger.error(f"保存代理数据失败: {e}")
            return ""
            
    def reset_failed_proxies(self) -> int:
        """重置失败的代理"""
        count = 0
        for proxy in self.proxies:
            if proxy.status == ProxyStatus.FAILED:
                proxy.status = ProxyStatus.UNKNOWN
                proxy.failure_count = 0
                count += 1
                
        logger.info(f"🔄 重置了 {count} 个失败的代理")
        return count

# 全局代理管理器
global_proxy_manager = ProxyManager()

def add_proxy(host: str, port: int, username: str = None, password: str = None):
    """添加代理到全局管理器"""
    global_proxy_manager.add_proxy(host, port, username, password)

def get_proxy() -> Optional[ProxyInfo]:
    """获取代理"""
    return global_proxy_manager.get_next_proxy()

async def check_proxies():
    """检查所有代理"""
    return await global_proxy_manager.check_all_proxies()

if __name__ == "__main__":
    # 测试代理管理器
    async def test_proxy_manager():
        manager = ProxyManager()
        
        # 添加一些测试代理
        manager.add_proxy("127.0.0.1", 8080)
        manager.add_proxy("proxy.example.com", 3128)
        
        # 检查代理
        results = await manager.check_all_proxies()
        print(f"检查结果: {results}")
        
        # 生成报告
        report = manager.generate_report()
        print(report)
        
    asyncio.run(test_proxy_manager())
