<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title data-i18n="optionsTitle"></title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #2a303c;
            color: #ffffff;
            padding: 20px;
            line-height: 1.6;
        }

        h1 {
            font-size: 28px;
            margin-bottom: 20px;
            color: white;
        }

        p {
            margin-bottom: 20px;
            font-size: 16px;
            color: #899DA7;
        }

        .content {
            max-width: 800px;
            margin: 0 auto;
            background-color: #252a34;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .content p {
            margin-bottom: 20px;
        }

        .content h2 {
            font-size: 22px;
            margin-bottom: 15px;
            color: #ffffff;
        }

        a {
            color: #d8c444;
            text-decoration: none;
        }

        a:hover {
            text-decoration: underline;
        }

        .font-bold {
            font-weight: bold;
        }

        .mt-0 {
            margin-top: 0;
        }

        .text-warning {
            color: #efd057;
        }
    </style>
</head>
<body>
<div class="content">
    <h1 class="mt-0" data-i18n="optionsTitle"></h1>
    <h2 data-i18n="cspExplanationTitle"></h2>
    <p data-i18n="cspExplanation"></p>
    <h2 data-i18n="cspWarningTitle"></h2>
    <p data-i18n="cspWarning"></p>
    <h2 data-i18n="cspDisclaimerTitle"></h2>
    <p class="font-bold text-warning" data-i18n="cspDisclaimer"></p>
</div>
<script src="options.js"></script>
</body>
</html>
