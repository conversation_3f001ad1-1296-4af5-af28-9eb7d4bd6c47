#!/usr/bin/env python3
"""
爬虫调度器
实现任务调度、并发控制、资源管理等高级功能
"""

import asyncio
import time
import logging
from typing import List, Dict, Any, Optional, Callable
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta
import json
import uuid

from position_crawler_core import PositionCrawlerCore
from performance_monitor import PerformanceMonitor
from proxy_manager import ProxyManager
from smart_retry import SMART_RETRY

logger = logging.getLogger(__name__)

class TaskStatus(Enum):
    """任务状态"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    RETRYING = "retrying"

class TaskPriority(Enum):
    """任务优先级"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4

@dataclass
class CrawlTask:
    """爬取任务"""
    task_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    city: str = ""
    keyword: str = ""
    page: int = 1
    priority: TaskPriority = TaskPriority.NORMAL
    status: TaskStatus = TaskStatus.PENDING
    created_at: float = field(default_factory=time.time)
    started_at: Optional[float] = None
    completed_at: Optional[float] = None
    retry_count: int = 0
    max_retries: int = 3
    result: Optional[List[str]] = None
    error: Optional[str] = None
    execution_time: float = 0.0
    
    @property
    def age(self) -> float:
        """任务年龄（秒）"""
        return time.time() - self.created_at
        
    @property
    def is_expired(self) -> bool:
        """是否过期（超过1小时）"""
        return self.age > 3600
        
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "task_id": self.task_id,
            "city": self.city,
            "keyword": self.keyword,
            "page": self.page,
            "priority": self.priority.value,
            "status": self.status.value,
            "created_at": self.created_at,
            "started_at": self.started_at,
            "completed_at": self.completed_at,
            "retry_count": self.retry_count,
            "max_retries": self.max_retries,
            "result_count": len(self.result) if self.result else 0,
            "error": self.error,
            "execution_time": self.execution_time,
            "age": self.age
        }

class CrawlerScheduler:
    """爬虫调度器"""
    
    def __init__(self, max_concurrent: int = 3, enable_monitoring: bool = True):
        self.max_concurrent = max_concurrent
        self.enable_monitoring = enable_monitoring
        
        # 任务队列
        self.pending_tasks: List[CrawlTask] = []
        self.running_tasks: Dict[str, CrawlTask] = {}
        self.completed_tasks: List[CrawlTask] = []
        self.failed_tasks: List[CrawlTask] = []
        
        # 组件
        self.crawler = PositionCrawlerCore()
        self.performance_monitor = PerformanceMonitor() if enable_monitoring else None
        self.proxy_manager = ProxyManager()
        
        # 控制变量
        self.is_running = False
        self.scheduler_task: Optional[asyncio.Task] = None
        self.worker_tasks: List[asyncio.Task] = []
        
        # 统计信息
        self.stats = {
            "total_tasks": 0,
            "completed_tasks": 0,
            "failed_tasks": 0,
            "total_urls": 0,
            "start_time": None,
            "end_time": None
        }
        
    def add_task(self, city: str, keyword: str, page: int = 1, 
                 priority: TaskPriority = TaskPriority.NORMAL) -> str:
        """添加任务"""
        task = CrawlTask(
            city=city,
            keyword=keyword,
            page=page,
            priority=priority
        )
        
        self.pending_tasks.append(task)
        self.stats["total_tasks"] += 1
        
        # 按优先级排序
        self.pending_tasks.sort(key=lambda t: (-t.priority.value, t.created_at))
        
        logger.info(f"📋 添加任务: {city} + {keyword} (页面{page})")
        return task.task_id
        
    def add_batch_tasks(self, cities: List[str], keywords: List[str], 
                       max_pages: int = 5, priority: TaskPriority = TaskPriority.NORMAL) -> List[str]:
        """批量添加任务"""
        task_ids = []
        
        for city in cities:
            for keyword in keywords:
                for page in range(1, max_pages + 1):
                    task_id = self.add_task(city, keyword, page, priority)
                    task_ids.append(task_id)
                    
        logger.info(f"📋 批量添加了 {len(task_ids)} 个任务")
        return task_ids
        
    def get_next_task(self) -> Optional[CrawlTask]:
        """获取下一个任务"""
        if not self.pending_tasks:
            return None
            
        # 移除过期任务
        self.pending_tasks = [t for t in self.pending_tasks if not t.is_expired]
        
        if not self.pending_tasks:
            return None
            
        # 返回优先级最高的任务
        task = self.pending_tasks.pop(0)
        task.status = TaskStatus.RUNNING
        task.started_at = time.time()
        self.running_tasks[task.task_id] = task
        
        return task
        
    async def execute_task(self, task: CrawlTask) -> None:
        """执行任务"""
        start_time = time.time()
        
        try:
            logger.info(f"🚀 执行任务: {task.city} + {task.keyword} (页面{task.page})")
            
            # 构造搜索参数
            search_params = {
                'city_code': self._get_city_code(task.city),
                'keyword': task.keyword,
                'page': task.page
            }
            
            # 执行爬取
            urls = self.crawler.use_seleniumbase_stealth(search_params)
            
            if urls:
                task.result = urls
                task.status = TaskStatus.COMPLETED
                task.completed_at = time.time()
                task.execution_time = time.time() - start_time
                
                self.completed_tasks.append(task)
                self.stats["completed_tasks"] += 1
                self.stats["total_urls"] += len(urls)
                
                logger.info(f"✅ 任务完成: {task.city} + {task.keyword}, 获取 {len(urls)} 个URL")
                
            else:
                raise Exception("未获取到任何URL")
                
        except Exception as e:
            task.error = str(e)
            task.execution_time = time.time() - start_time
            
            # 判断是否重试
            if task.retry_count < task.max_retries:
                task.retry_count += 1
                task.status = TaskStatus.RETRYING
                
                # 计算重试延迟
                delay = SMART_RETRY.calculate_delay(
                    SMART_RETRY.classify_error(str(e)), 
                    task.retry_count
                )
                
                logger.warning(f"⚠️ 任务失败，{delay:.1f}秒后重试: {task.city} + {task.keyword}")
                
                # 延迟后重新加入队列
                await asyncio.sleep(delay)
                task.status = TaskStatus.PENDING
                self.pending_tasks.append(task)
                self.pending_tasks.sort(key=lambda t: (-t.priority.value, t.created_at))
                
            else:
                task.status = TaskStatus.FAILED
                task.completed_at = time.time()
                
                self.failed_tasks.append(task)
                self.stats["failed_tasks"] += 1
                
                logger.error(f"❌ 任务最终失败: {task.city} + {task.keyword} - {e}")
                
        finally:
            # 从运行中任务移除
            if task.task_id in self.running_tasks:
                del self.running_tasks[task.task_id]
                
    async def worker(self, worker_id: int) -> None:
        """工作线程"""
        logger.info(f"👷 工作线程 {worker_id} 启动")
        
        while self.is_running:
            try:
                # 检查并发限制
                if len(self.running_tasks) >= self.max_concurrent:
                    await asyncio.sleep(1)
                    continue
                    
                # 获取任务
                task = self.get_next_task()
                if not task:
                    await asyncio.sleep(2)
                    continue
                    
                # 执行任务
                await self.execute_task(task)
                
                # 任务间延迟
                await asyncio.sleep(random.uniform(2, 5))
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"工作线程 {worker_id} 异常: {e}")
                await asyncio.sleep(5)
                
        logger.info(f"👷 工作线程 {worker_id} 停止")
        
    async def scheduler_loop(self) -> None:
        """调度器主循环"""
        logger.info("📅 调度器主循环启动")
        
        while self.is_running:
            try:
                # 清理过期任务
                self._cleanup_expired_tasks()
                
                # 检查系统资源
                if self.performance_monitor:
                    status = self.performance_monitor.get_current_status()
                    if status.get("current_metrics", {}).get("cpu_percent", 0) > 90:
                        logger.warning("⚠️ CPU使用率过高，暂停调度")
                        await asyncio.sleep(10)
                        continue
                        
                # 动态调整并发数
                self._adjust_concurrency()
                
                # 状态报告
                if int(time.time()) % 60 == 0:  # 每分钟报告一次
                    self._log_status()
                    
                await asyncio.sleep(5)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"调度器主循环异常: {e}")
                await asyncio.sleep(10)
                
        logger.info("📅 调度器主循环停止")
        
    def start(self) -> None:
        """启动调度器"""
        if self.is_running:
            logger.warning("调度器已在运行")
            return
            
        self.is_running = True
        self.stats["start_time"] = time.time()
        
        # 启动性能监控
        if self.performance_monitor:
            self.performance_monitor.start_monitoring()
            
        # 启动调度器主循环
        self.scheduler_task = asyncio.create_task(self.scheduler_loop())
        
        # 启动工作线程
        self.worker_tasks = [
            asyncio.create_task(self.worker(i))
            for i in range(self.max_concurrent)
        ]
        
        logger.info(f"🚀 调度器已启动，并发数: {self.max_concurrent}")
        
    async def stop(self) -> None:
        """停止调度器"""
        if not self.is_running:
            return
            
        logger.info("⏹️ 正在停止调度器...")
        
        self.is_running = False
        self.stats["end_time"] = time.time()
        
        # 停止调度器主循环
        if self.scheduler_task:
            self.scheduler_task.cancel()
            try:
                await self.scheduler_task
            except asyncio.CancelledError:
                pass
                
        # 停止工作线程
        for task in self.worker_tasks:
            task.cancel()
            
        await asyncio.gather(*self.worker_tasks, return_exceptions=True)
        
        # 停止性能监控
        if self.performance_monitor:
            self.performance_monitor.stop_monitoring()
            
        logger.info("⏹️ 调度器已停止")
        
    def _cleanup_expired_tasks(self) -> None:
        """清理过期任务"""
        before_count = len(self.pending_tasks)
        self.pending_tasks = [t for t in self.pending_tasks if not t.is_expired]
        after_count = len(self.pending_tasks)
        
        if before_count > after_count:
            logger.info(f"🧹 清理了 {before_count - after_count} 个过期任务")
            
    def _adjust_concurrency(self) -> None:
        """动态调整并发数"""
        if not self.performance_monitor:
            return
            
        status = self.performance_monitor.get_current_status()
        current_metrics = status.get("current_metrics", {})
        
        cpu_percent = current_metrics.get("cpu_percent", 0)
        memory_percent = current_metrics.get("memory_percent", 0)
        
        # 根据资源使用情况调整并发数
        if cpu_percent > 80 or memory_percent > 80:
            if self.max_concurrent > 1:
                self.max_concurrent -= 1
                logger.info(f"📉 降低并发数到: {self.max_concurrent}")
        elif cpu_percent < 50 and memory_percent < 50:
            if self.max_concurrent < 5:
                self.max_concurrent += 1
                logger.info(f"📈 提高并发数到: {self.max_concurrent}")
                
    def _log_status(self) -> None:
        """记录状态"""
        pending = len(self.pending_tasks)
        running = len(self.running_tasks)
        completed = len(self.completed_tasks)
        failed = len(self.failed_tasks)
        
        logger.info(
            f"📊 调度器状态: 待处理={pending}, 运行中={running}, "
            f"已完成={completed}, 失败={failed}"
        )
        
    def _get_city_code(self, city: str) -> str:
        """获取城市代码"""
        city_codes = {
            "北京": "101010100",
            "上海": "101020100",
            "深圳": "101280600",
            "杭州": "101210100",
            "广州": "101280100"
        }
        return city_codes.get(city, "")
        
    def get_status_summary(self) -> Dict[str, Any]:
        """获取状态摘要"""
        runtime = time.time() - self.stats["start_time"] if self.stats["start_time"] else 0
        
        return {
            "is_running": self.is_running,
            "runtime_seconds": runtime,
            "runtime_formatted": self._format_duration(runtime),
            "max_concurrent": self.max_concurrent,
            "task_counts": {
                "pending": len(self.pending_tasks),
                "running": len(self.running_tasks),
                "completed": len(self.completed_tasks),
                "failed": len(self.failed_tasks),
                "total": self.stats["total_tasks"]
            },
            "performance": {
                "total_urls": self.stats["total_urls"],
                "avg_urls_per_task": self.stats["total_urls"] / max(self.stats["completed_tasks"], 1),
                "success_rate": self.stats["completed_tasks"] / max(self.stats["total_tasks"], 1) * 100
            }
        }
        
    def generate_report(self) -> str:
        """生成调度报告"""
        summary = self.get_status_summary()
        
        report_lines = []
        report_lines.append("=" * 80)
        report_lines.append("📅 爬虫调度器报告")
        report_lines.append("=" * 80)
        report_lines.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append("")
        
        # 运行状态
        report_lines.append("🚀 运行状态")
        report_lines.append("-" * 40)
        report_lines.append(f"状态: {'运行中' if summary['is_running'] else '已停止'}")
        report_lines.append(f"运行时长: {summary['runtime_formatted']}")
        report_lines.append(f"并发数: {summary['max_concurrent']}")
        report_lines.append("")
        
        # 任务统计
        task_counts = summary["task_counts"]
        report_lines.append("📋 任务统计")
        report_lines.append("-" * 40)
        report_lines.append(f"总任务数: {task_counts['total']}")
        report_lines.append(f"待处理: {task_counts['pending']}")
        report_lines.append(f"运行中: {task_counts['running']}")
        report_lines.append(f"已完成: {task_counts['completed']}")
        report_lines.append(f"失败: {task_counts['failed']}")
        report_lines.append("")
        
        # 性能统计
        performance = summary["performance"]
        report_lines.append("📊 性能统计")
        report_lines.append("-" * 40)
        report_lines.append(f"总URL数: {performance['total_urls']}")
        report_lines.append(f"平均每任务URL数: {performance['avg_urls_per_task']:.1f}")
        report_lines.append(f"成功率: {performance['success_rate']:.1f}%")
        report_lines.append("")
        
        # 最近的失败任务
        if self.failed_tasks:
            report_lines.append("❌ 最近失败任务")
            report_lines.append("-" * 40)
            for task in self.failed_tasks[-5:]:  # 最近5个失败任务
                report_lines.append(f"• {task.city} + {task.keyword} - {task.error}")
            report_lines.append("")
            
        report_lines.append("=" * 80)
        
        return "\n".join(report_lines)
        
    def save_results(self, filename: str = None) -> str:
        """保存调度结果"""
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"scheduler_results_{timestamp}.json"
            
        try:
            # 收集所有URL
            all_urls = []
            for task in self.completed_tasks:
                if task.result:
                    all_urls.extend(task.result)
                    
            # 去重
            unique_urls = list(set(all_urls))
            
            data = {
                "metadata": {
                    "generated_at": datetime.now().isoformat(),
                    "total_tasks": self.stats["total_tasks"],
                    "completed_tasks": self.stats["completed_tasks"],
                    "failed_tasks": self.stats["failed_tasks"],
                    "total_urls": len(all_urls),
                    "unique_urls": len(unique_urls)
                },
                "summary": self.get_status_summary(),
                "completed_tasks": [task.to_dict() for task in self.completed_tasks],
                "failed_tasks": [task.to_dict() for task in self.failed_tasks],
                "urls": unique_urls
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
            # 同时保存纯URL文件
            url_filename = filename.replace('.json', '_urls.txt')
            with open(url_filename, 'w', encoding='utf-8') as f:
                f.write(f"# 调度器爬取结果 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"# 总任务数: {self.stats['total_tasks']}\n")
                f.write(f"# 成功任务: {self.stats['completed_tasks']}\n")
                f.write(f"# 唯一URL数: {len(unique_urls)}\n\n")
                
                for url in unique_urls:
                    f.write(url + '\n')
                    
            logger.info(f"💾 调度结果已保存到: {filename} 和 {url_filename}")
            return filename
            
        except Exception as e:
            logger.error(f"保存调度结果失败: {e}")
            return ""
            
    def _format_duration(self, seconds: float) -> str:
        """格式化时长"""
        if seconds < 60:
            return f"{seconds:.1f}秒"
        elif seconds < 3600:
            return f"{seconds/60:.1f}分钟"
        else:
            return f"{seconds/3600:.1f}小时"

if __name__ == "__main__":
    # 测试调度器
    async def test_scheduler():
        scheduler = CrawlerScheduler(max_concurrent=2)
        
        # 添加测试任务
        scheduler.add_batch_tasks(
            cities=["北京", "上海"],
            keywords=["Python", "Java"],
            max_pages=2
        )
        
        # 启动调度器
        scheduler.start()
        
        # 运行一段时间
        await asyncio.sleep(30)
        
        # 停止调度器
        await scheduler.stop()
        
        # 生成报告
        report = scheduler.generate_report()
        print(report)
        
        # 保存结果
        scheduler.save_results()
        
    asyncio.run(test_scheduler())
