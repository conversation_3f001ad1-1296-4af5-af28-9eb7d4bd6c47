#!/usr/bin/env python3
"""
项目健康检查工具
检查项目状态、依赖、配置等
"""

import os
import sys
import json
import time
import logging
import subprocess
from pathlib import Path
from typing import Dict, List, Any

logger = logging.getLogger(__name__)

class HealthChecker:
    """项目健康检查器"""
    
    def __init__(self):
        self.project_root = Path.cwd()
        self.checks = []
        self.results = {}
        
    def check_python_environment(self) -> Dict[str, Any]:
        """检查Python环境"""
        result = {
            "name": "Python环境",
            "status": "unknown",
            "details": {},
            "issues": []
        }
        
        try:
            # Python版本
            version = sys.version_info
            result["details"]["python_version"] = f"{version.major}.{version.minor}.{version.micro}"
            
            if version < (3, 8):
                result["status"] = "error"
                result["issues"].append("Python版本过低，需要3.8+")
            else:
                result["status"] = "ok"
                
            # 虚拟环境
            result["details"]["virtual_env"] = hasattr(sys, 'real_prefix') or (
                hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix
            )
            
            # 已安装包
            try:
                import pkg_resources
                installed_packages = [d.project_name for d in pkg_resources.working_set]
                result["details"]["installed_packages_count"] = len(installed_packages)
            except:
                result["details"]["installed_packages_count"] = "unknown"
                
        except Exception as e:
            result["status"] = "error"
            result["issues"].append(f"Python环境检查失败: {e}")
            
        return result
        
    def check_dependencies(self) -> Dict[str, Any]:
        """检查依赖"""
        result = {
            "name": "项目依赖",
            "status": "unknown",
            "details": {},
            "issues": []
        }
        
        try:
            # 检查requirements.txt
            req_file = self.project_root / "requirements.txt"
            if not req_file.exists():
                result["status"] = "warning"
                result["issues"].append("requirements.txt文件不存在")
                return result
                
            # 读取依赖列表
            with open(req_file, 'r', encoding='utf-8') as f:
                requirements = [line.strip() for line in f if line.strip() and not line.startswith('#')]
                
            result["details"]["total_requirements"] = len(requirements)
            
            # 检查关键依赖
            critical_deps = [
                "beautifulsoup4", "lxml", "aiohttp", "requests",
                "seleniumbase", "playwright", "cloudscraper"
            ]
            
            missing_deps = []
            available_deps = []
            
            for dep in critical_deps:
                try:
                    __import__(dep.replace('-', '_'))
                    available_deps.append(dep)
                except ImportError:
                    missing_deps.append(dep)
                    
            result["details"]["available_critical_deps"] = len(available_deps)
            result["details"]["missing_critical_deps"] = len(missing_deps)
            
            if missing_deps:
                result["status"] = "warning"
                result["issues"].append(f"缺少关键依赖: {', '.join(missing_deps)}")
            else:
                result["status"] = "ok"
                
        except Exception as e:
            result["status"] = "error"
            result["issues"].append(f"依赖检查失败: {e}")
            
        return result
        
    def check_project_structure(self) -> Dict[str, Any]:
        """检查项目结构"""
        result = {
            "name": "项目结构",
            "status": "unknown",
            "details": {},
            "issues": []
        }
        
        try:
            # 必需文件
            required_files = [
                "main.py",
                "position_crawler_core.py",
                "project_manager.py",
                "crawler_config.py",
                "requirements.txt"
            ]
            
            missing_files = []
            existing_files = []
            
            for file in required_files:
                if (self.project_root / file).exists():
                    existing_files.append(file)
                else:
                    missing_files.append(file)
                    
            result["details"]["existing_files"] = len(existing_files)
            result["details"]["missing_files"] = len(missing_files)
            
            # 必需目录
            required_dirs = ["output"]
            missing_dirs = []
            
            for dir_name in required_dirs:
                if not (self.project_root / dir_name).exists():
                    missing_dirs.append(dir_name)
                    
            if missing_files or missing_dirs:
                result["status"] = "warning"
                if missing_files:
                    result["issues"].append(f"缺少文件: {', '.join(missing_files)}")
                if missing_dirs:
                    result["issues"].append(f"缺少目录: {', '.join(missing_dirs)}")
            else:
                result["status"] = "ok"
                
        except Exception as e:
            result["status"] = "error"
            result["issues"].append(f"项目结构检查失败: {e}")
            
        return result
        
    def check_configuration(self) -> Dict[str, Any]:
        """检查配置"""
        result = {
            "name": "配置文件",
            "status": "unknown",
            "details": {},
            "issues": []
        }
        
        try:
            # 检查配置文件
            config_file = self.project_root / "crawler_config.json"
            
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    
                result["details"]["config_keys"] = len(config.keys())
                
                # 检查必需配置项
                required_keys = ["base_url", "delay_min", "delay_max", "target_cities", "target_keywords"]
                missing_keys = [key for key in required_keys if key not in config]
                
                if missing_keys:
                    result["status"] = "warning"
                    result["issues"].append(f"缺少配置项: {', '.join(missing_keys)}")
                else:
                    result["status"] = "ok"
            else:
                result["status"] = "warning"
                result["issues"].append("配置文件不存在")
                
            # 检查配置模块
            try:
                from crawler_config import get_config
                config = get_config()
                result["details"]["config_module_ok"] = True
            except Exception as e:
                result["details"]["config_module_ok"] = False
                result["issues"].append(f"配置模块加载失败: {e}")
                
        except Exception as e:
            result["status"] = "error"
            result["issues"].append(f"配置检查失败: {e}")
            
        return result
        
    def check_modules(self) -> Dict[str, Any]:
        """检查项目模块"""
        result = {
            "name": "项目模块",
            "status": "unknown",
            "details": {},
            "issues": []
        }
        
        try:
            modules = [
                "position_crawler_core",
                "advanced_stealth",
                "smart_retry", 
                "data_analyzer",
                "url_validator",
                "performance_monitor",
                "proxy_manager",
                "crawler_scheduler",
                "project_manager"
            ]
            
            working_modules = []
            broken_modules = []
            
            for module in modules:
                try:
                    __import__(module)
                    working_modules.append(module)
                except Exception as e:
                    broken_modules.append((module, str(e)))
                    
            result["details"]["working_modules"] = len(working_modules)
            result["details"]["broken_modules"] = len(broken_modules)
            
            if broken_modules:
                result["status"] = "warning"
                for module, error in broken_modules:
                    result["issues"].append(f"模块 {module} 导入失败: {error}")
            else:
                result["status"] = "ok"
                
        except Exception as e:
            result["status"] = "error"
            result["issues"].append(f"模块检查失败: {e}")
            
        return result
        
    def check_disk_space(self) -> Dict[str, Any]:
        """检查磁盘空间"""
        result = {
            "name": "磁盘空间",
            "status": "unknown",
            "details": {},
            "issues": []
        }
        
        try:
            import shutil
            total, used, free = shutil.disk_usage(self.project_root)
            
            result["details"]["total_gb"] = round(total / (1024**3), 2)
            result["details"]["used_gb"] = round(used / (1024**3), 2)
            result["details"]["free_gb"] = round(free / (1024**3), 2)
            result["details"]["usage_percent"] = round((used / total) * 100, 1)
            
            if free < 1024**3:  # 小于1GB
                result["status"] = "warning"
                result["issues"].append("可用磁盘空间不足1GB")
            elif free < 100 * 1024**2:  # 小于100MB
                result["status"] = "error"
                result["issues"].append("可用磁盘空间严重不足")
            else:
                result["status"] = "ok"
                
        except Exception as e:
            result["status"] = "error"
            result["issues"].append(f"磁盘空间检查失败: {e}")
            
        return result
        
    def run_all_checks(self) -> Dict[str, Any]:
        """运行所有检查"""
        print("🔍 开始项目健康检查...")
        
        checks = [
            self.check_python_environment,
            self.check_dependencies,
            self.check_project_structure,
            self.check_configuration,
            self.check_modules,
            self.check_disk_space
        ]
        
        results = []
        overall_status = "ok"
        
        for check_func in checks:
            print(f"   检查: {check_func.__name__.replace('check_', '').replace('_', ' ')}")
            result = check_func()
            results.append(result)
            
            if result["status"] == "error":
                overall_status = "error"
            elif result["status"] == "warning" and overall_status == "ok":
                overall_status = "warning"
                
        return {
            "timestamp": time.time(),
            "overall_status": overall_status,
            "checks": results
        }
        
    def generate_report(self, results: Dict[str, Any]) -> str:
        """生成健康检查报告"""
        report_lines = []
        report_lines.append("=" * 80)
        report_lines.append("🏥 项目健康检查报告")
        report_lines.append("=" * 80)
        report_lines.append(f"检查时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 总体状态
        status_icons = {"ok": "✅", "warning": "⚠️", "error": "❌"}
        overall_icon = status_icons.get(results["overall_status"], "❓")
        report_lines.append(f"总体状态: {overall_icon} {results['overall_status'].upper()}")
        report_lines.append("")
        
        # 详细检查结果
        for check in results["checks"]:
            icon = status_icons.get(check["status"], "❓")
            report_lines.append(f"{icon} {check['name']}")
            
            # 详细信息
            if check["details"]:
                for key, value in check["details"].items():
                    report_lines.append(f"   {key}: {value}")
                    
            # 问题列表
            if check["issues"]:
                for issue in check["issues"]:
                    report_lines.append(f"   ⚠️ {issue}")
                    
            report_lines.append("")
            
        # 建议
        report_lines.append("💡 建议:")
        if results["overall_status"] == "ok":
            report_lines.append("   项目状态良好，可以正常使用")
        elif results["overall_status"] == "warning":
            report_lines.append("   存在一些问题，建议修复后使用")
        else:
            report_lines.append("   存在严重问题，需要修复后才能使用")
            
        report_lines.append("")
        report_lines.append("=" * 80)
        
        return "\n".join(report_lines)
        
    def save_report(self, results: Dict[str, Any], filename: str = None) -> str:
        """保存健康检查报告"""
        if not filename:
            timestamp = time.strftime('%Y%m%d_%H%M%S')
            filename = f"health_check_{timestamp}.json"
            
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            return filename
        except Exception as e:
            logger.error(f"保存报告失败: {e}")
            return ""

def main():
    """主函数"""
    checker = HealthChecker()
    
    # 运行检查
    results = checker.run_all_checks()
    
    # 生成报告
    report = checker.generate_report(results)
    print(report)
    
    # 保存报告
    filename = checker.save_report(results)
    if filename:
        print(f"📄 详细报告已保存到: {filename}")
        
    # 返回状态码
    if results["overall_status"] == "error":
        sys.exit(1)
    elif results["overall_status"] == "warning":
        sys.exit(2)
    else:
        sys.exit(0)

if __name__ == "__main__":
    main()
