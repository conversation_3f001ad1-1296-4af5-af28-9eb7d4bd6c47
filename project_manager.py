#!/usr/bin/env python3
"""
项目管理工具
提供项目的统一管理和操作界面
"""

import os
import sys
import time
import asyncio
import logging
from pathlib import Path
from typing import List, Dict, Any

# 导入项目模块
from position_crawler_core import PositionCrawlerCore
from data_analyzer import URLAnalyzer, analyze_crawled_data
from url_validator import validate_url_file
from crawler_config import config_manager, get_config
from project_manager_extensions import extend_project_manager

logger = logging.getLogger(__name__)

@extend_project_manager
class ProjectManager:
    """项目管理器"""

    def __init__(self):
        self.project_root = Path.cwd()
        self.output_dir = Path("output")
        self.output_dir.mkdir(exist_ok=True)
        
    def show_menu(self) -> None:
        """显示主菜单"""
        print("\n" + "=" * 80)
        print("🚀 BOSS直聘爬虫项目管理器")
        print("=" * 80)
        print("1. 🕷️  运行爬虫")
        print("2. 📅  智能调度器")
        print("3. 📊  分析数据")
        print("4. 🔍  验证URL")
        print("5. 🌐  代理管理")
        print("6. 📈  性能监控")
        print("7. ⚙️  配置管理")
        print("8. 📁  文件管理")
        print("9. 📋  项目状态")
        print("10. 🧹  清理项目")
        print("11. 📖  帮助文档")
        print("12. 🚪  退出")
        print("=" * 80)
        
    def run_crawler(self) -> None:
        """运行爬虫"""
        print("\n🕷️ 启动爬虫...")
        
        try:
            config = get_config()
            crawler = PositionCrawlerCore()
            
            print(f"🎯 目标城市: {', '.join(config.target_cities[:3])}")
            print(f"🔍 搜索关键词: {', '.join(config.target_keywords[:3])}")
            
            # 运行爬虫
            urls = crawler.crawl_boss_jobs(
                cities=config.target_cities[:3],
                keywords=config.target_keywords[:3]
            )
            
            if urls:
                # 保存结果
                filepath = crawler.save_results()
                crawler.print_statistics()
                
                print(f"\n✅ 爬虫完成！")
                print(f"📊 获取URL数量: {len(urls)}")
                print(f"💾 结果文件: {filepath}")
                
                # 询问是否进行后续分析
                choice = input("\n是否立即分析数据？(y/n): ").lower()
                if choice == 'y':
                    self.analyze_data(filepath)
                    
            else:
                print("❌ 未获取到任何URL")
                print("💡 建议检查网络连接或调整配置参数")
                
        except Exception as e:
            print(f"❌ 爬虫运行失败: {e}")
            logger.exception("爬虫运行异常")

    def run_scheduler(self) -> None:
        """运行智能调度器"""
        print("\n📅 智能调度器...")

        try:
            from crawler_scheduler import CrawlerScheduler
            import asyncio

            config = get_config()

            print("调度器配置:")
            print("1. 快速测试 (2个城市 x 2个关键词 x 2页)")
            print("2. 标准任务 (3个城市 x 3个关键词 x 3页)")
            print("3. 大规模任务 (5个城市 x 5个关键词 x 5页)")
            print("4. 自定义配置")

            choice = input("请选择配置: ").strip()

            if choice == '1':
                cities = config.target_cities[:2]
                keywords = config.target_keywords[:2]
                max_pages = 2
                max_concurrent = 2
            elif choice == '2':
                cities = config.target_cities[:3]
                keywords = config.target_keywords[:3]
                max_pages = 3
                max_concurrent = 3
            elif choice == '3':
                cities = config.target_cities[:5]
                keywords = config.target_keywords[:5]
                max_pages = 5
                max_concurrent = 3
            elif choice == '4':
                cities_input = input("请输入城市列表(用逗号分隔): ").strip()
                keywords_input = input("请输入关键词列表(用逗号分隔): ").strip()
                max_pages = int(input("请输入最大页数: ") or "3")
                max_concurrent = int(input("请输入并发数: ") or "3")

                cities = [c.strip() for c in cities_input.split(',') if c.strip()]
                keywords = [k.strip() for k in keywords_input.split(',') if k.strip()]
            else:
                print("❌ 无效的选择")
                return

            print(f"\n🎯 调度器配置:")
            print(f"   城市: {', '.join(cities)}")
            print(f"   关键词: {', '.join(keywords)}")
            print(f"   最大页数: {max_pages}")
            print(f"   并发数: {max_concurrent}")

            confirm = input("\n确认启动调度器？(y/n): ").lower()
            if confirm != 'y':
                print("取消启动")
                return

            # 启动调度器
            async def run_scheduler_async():
                scheduler = CrawlerScheduler(max_concurrent=max_concurrent)

                # 添加任务
                task_ids = scheduler.add_batch_tasks(cities, keywords, max_pages)
                print(f"📋 已添加 {len(task_ids)} 个任务")

                # 启动调度器
                scheduler.start()
                print("🚀 调度器已启动，按 Ctrl+C 停止...")

                try:
                    # 等待所有任务完成或用户中断
                    while scheduler.is_running:
                        status = scheduler.get_status_summary()
                        pending = status["task_counts"]["pending"]
                        running = status["task_counts"]["running"]

                        if pending == 0 and running == 0:
                            print("✅ 所有任务已完成")
                            break

                        await asyncio.sleep(10)

                except KeyboardInterrupt:
                    print("\n⚠️ 用户中断，正在停止调度器...")

                finally:
                    await scheduler.stop()

                    # 生成报告
                    report = scheduler.generate_report()
                    print(report)

                    # 保存结果
                    result_file = scheduler.save_results()
                    if result_file:
                        print(f"💾 结果已保存到: {result_file}")

            asyncio.run(run_scheduler_async())

        except ImportError:
            print("❌ 调度器模块未找到")
        except Exception as e:
            print(f"❌ 调度器运行失败: {e}")

    def analyze_data(self, filepath: str = None) -> None:
        """分析数据"""
        print("\n📊 数据分析...")
        
        if not filepath:
            # 选择文件
            files = self.list_output_files()
            if not files:
                print("❌ 没有找到数据文件")
                return
                
            print("请选择要分析的文件:")
            for i, file in enumerate(files, 1):
                print(f"{i}. {file}")
                
            try:
                choice = int(input("请输入文件编号: ")) - 1
                if 0 <= choice < len(files):
                    filepath = files[choice]
                else:
                    print("❌ 无效的选择")
                    return
            except ValueError:
                print("❌ 请输入有效的数字")
                return
                
        try:
            analyze_crawled_data(filepath)
            print(f"✅ 数据分析完成")
            
        except Exception as e:
            print(f"❌ 数据分析失败: {e}")
            
    def validate_urls(self) -> None:
        """验证URL"""
        print("\n🔍 URL验证...")
        
        # 选择文件
        files = self.list_output_files()
        if not files:
            print("❌ 没有找到数据文件")
            return
            
        print("请选择要验证的文件:")
        for i, file in enumerate(files, 1):
            print(f"{i}. {file}")
            
        try:
            choice = int(input("请输入文件编号: ")) - 1
            if 0 <= choice < len(files):
                filepath = files[choice]
            else:
                print("❌ 无效的选择")
                return
        except ValueError:
            print("❌ 请输入有效的数字")
            return
            
        try:
            print("🔍 开始验证URL...")
            asyncio.run(validate_url_file(filepath))
            print("✅ URL验证完成")
            
        except Exception as e:
            print(f"❌ URL验证失败: {e}")
            
    def manage_config(self) -> None:
        """配置管理"""
        print("\n⚙️ 配置管理...")
        
        config = get_config()
        
        while True:
            print("\n配置选项:")
            print("1. 查看当前配置")
            print("2. 修改目标城市")
            print("3. 修改搜索关键词")
            print("4. 修改延迟设置")
            print("5. 修改重试设置")
            print("6. 返回主菜单")
            
            choice = input("请选择操作: ").strip()
            
            if choice == '1':
                config_manager.print_config_summary()
                
            elif choice == '2':
                print(f"当前城市: {', '.join(config.target_cities)}")
                new_cities = input("请输入新的城市列表(用逗号分隔): ").strip()
                if new_cities:
                    cities = [city.strip() for city in new_cities.split(',')]
                    config_manager.update_config(target_cities=cities)
                    print("✅ 城市配置已更新")
                    
            elif choice == '3':
                print(f"当前关键词: {', '.join(config.target_keywords)}")
                new_keywords = input("请输入新的关键词列表(用逗号分隔): ").strip()
                if new_keywords:
                    keywords = [kw.strip() for kw in new_keywords.split(',')]
                    config_manager.update_config(target_keywords=keywords)
                    print("✅ 关键词配置已更新")
                    
            elif choice == '4':
                print(f"当前延迟: {config.delay_min}-{config.delay_max}秒")
                try:
                    min_delay = float(input("请输入最小延迟(秒): "))
                    max_delay = float(input("请输入最大延迟(秒): "))
                    if min_delay < max_delay:
                        config_manager.update_config(delay_min=min_delay, delay_max=max_delay)
                        print("✅ 延迟配置已更新")
                    else:
                        print("❌ 最小延迟必须小于最大延迟")
                except ValueError:
                    print("❌ 请输入有效的数字")
                    
            elif choice == '5':
                print(f"当前重试次数: {config.max_retries}")
                try:
                    max_retries = int(input("请输入最大重试次数: "))
                    if max_retries > 0:
                        config_manager.update_config(max_retries=max_retries)
                        print("✅ 重试配置已更新")
                    else:
                        print("❌ 重试次数必须大于0")
                except ValueError:
                    print("❌ 请输入有效的数字")
                    
            elif choice == '6':
                break
                
            else:
                print("❌ 无效的选择")
                
    def manage_files(self) -> None:
        """文件管理"""
        print("\n📁 文件管理...")
        
        while True:
            print("\n文件操作:")
            print("1. 列出输出文件")
            print("2. 查看文件详情")
            print("3. 删除文件")
            print("4. 清理旧文件")
            print("5. 返回主菜单")
            
            choice = input("请选择操作: ").strip()
            
            if choice == '1':
                files = self.list_output_files()
                if files:
                    print("输出文件列表:")
                    for i, file in enumerate(files, 1):
                        size = os.path.getsize(file) / 1024  # KB
                        mtime = time.ctime(os.path.getmtime(file))
                        print(f"{i}. {os.path.basename(file)} ({size:.1f}KB, {mtime})")
                else:
                    print("❌ 没有找到输出文件")
                    
            elif choice == '2':
                files = self.list_output_files()
                if files:
                    self._show_file_details(files)
                else:
                    print("❌ 没有找到输出文件")
                    
            elif choice == '3':
                files = self.list_output_files()
                if files:
                    self._delete_file(files)
                else:
                    print("❌ 没有找到输出文件")
                    
            elif choice == '4':
                self._cleanup_old_files()
                
            elif choice == '5':
                break
                
            else:
                print("❌ 无效的选择")
                
    def show_project_status(self) -> None:
        """显示项目状态"""
        print("\n📋 项目状态...")
        
        print("=" * 60)
        print("📊 项目统计")
        print("=" * 60)
        
        # 文件统计
        output_files = self.list_output_files()
        print(f"输出文件数量: {len(output_files)}")
        
        if output_files:
            total_size = sum(os.path.getsize(f) for f in output_files) / 1024 / 1024  # MB
            print(f"总文件大小: {total_size:.2f}MB")
            
            latest_file = max(output_files, key=os.path.getmtime)
            latest_time = time.ctime(os.path.getmtime(latest_file))
            print(f"最新文件: {os.path.basename(latest_file)} ({latest_time})")
            
        # 配置状态
        config = get_config()
        print(f"目标城市数: {len(config.target_cities)}")
        print(f"搜索关键词数: {len(config.target_keywords)}")
        print(f"延迟设置: {config.delay_min}-{config.delay_max}秒")
        
        # 日志文件
        log_files = list(Path('.').glob('*.log'))
        print(f"日志文件数: {len(log_files)}")
        
        print("=" * 60)
        
    def cleanup_project(self) -> None:
        """清理项目"""
        print("\n🧹 项目清理...")
        
        print("清理选项:")
        print("1. 清理调试文件")
        print("2. 清理日志文件")
        print("3. 清理临时文件")
        print("4. 清理所有输出文件")
        print("5. 完全清理")
        print("6. 取消")
        
        choice = input("请选择清理选项: ").strip()
        
        if choice == '1':
            self._cleanup_debug_files()
        elif choice == '2':
            self._cleanup_log_files()
        elif choice == '3':
            self._cleanup_temp_files()
        elif choice == '4':
            self._cleanup_output_files()
        elif choice == '5':
            self._cleanup_all()
        elif choice == '6':
            print("取消清理")
        else:
            print("❌ 无效的选择")
            
    def show_help(self) -> None:
        """显示帮助文档"""
        print("\n📖 帮助文档...")
        
        help_text = """
🚀 BOSS直聘爬虫项目使用指南

📋 主要功能:
1. 爬虫功能 - 使用Position_Crawler_1核心技术爬取BOSS直聘职位URL
2. 数据分析 - 分析爬取的URL数据，生成统计报告
3. URL验证 - 验证URL的有效性和可访问性
4. 配置管理 - 管理爬虫参数和设置
5. 文件管理 - 管理输出文件和项目文件

🔧 配置说明:
- 延迟设置: 控制请求间隔，避免被反爬虫检测
- 重试设置: 控制失败重试次数和策略
- 目标城市: 设置要爬取的城市列表
- 搜索关键词: 设置职位搜索关键词

⚠️ 注意事项:
- 请合理设置延迟时间，避免对目标网站造成压力
- 遵守网站的robots.txt和使用条款
- 建议在非高峰时段运行爬虫
- 定期清理临时文件和日志

🆘 常见问题:
Q: 爬虫无法获取数据？
A: 检查网络连接，增加延迟时间，或使用代理

Q: 出现验证码？
A: 这是正常的反爬虫机制，建议使用Position_Crawler_1项目

Q: 如何提高成功率？
A: 增加延迟时间，使用代理IP，优化请求头

📞 技术支持:
- 查看日志文件获取详细错误信息
- 参考SOLUTION_GUIDE.md获取解决方案
- 考虑使用Position_Crawler_1项目的完整解决方案
        """
        
        print(help_text)
        
    def list_output_files(self) -> List[str]:
        """列出输出文件"""
        patterns = ['*.txt', '*.json', '*.html']
        files = []
        
        for pattern in patterns:
            files.extend(self.output_dir.glob(pattern))
            files.extend(Path('.').glob(pattern))
            
        return [str(f) for f in files if f.is_file()]
        
    def _show_file_details(self, files: List[str]) -> None:
        """显示文件详情"""
        print("请选择要查看的文件:")
        for i, file in enumerate(files, 1):
            print(f"{i}. {os.path.basename(file)}")
            
        try:
            choice = int(input("请输入文件编号: ")) - 1
            if 0 <= choice < len(files):
                filepath = files[choice]
                
                # 显示文件信息
                stat = os.stat(filepath)
                print(f"\n文件详情:")
                print(f"文件名: {os.path.basename(filepath)}")
                print(f"路径: {filepath}")
                print(f"大小: {stat.st_size / 1024:.1f}KB")
                print(f"创建时间: {time.ctime(stat.st_ctime)}")
                print(f"修改时间: {time.ctime(stat.st_mtime)}")
                
                # 显示文件内容预览
                if filepath.endswith('.txt'):
                    try:
                        with open(filepath, 'r', encoding='utf-8') as f:
                            lines = f.readlines()[:10]
                        print(f"\n内容预览 (前10行):")
                        for line in lines:
                            print(line.rstrip())
                    except:
                        print("无法读取文件内容")
                        
            else:
                print("❌ 无效的选择")
        except ValueError:
            print("❌ 请输入有效的数字")
            
    def _delete_file(self, files: List[str]) -> None:
        """删除文件"""
        print("请选择要删除的文件:")
        for i, file in enumerate(files, 1):
            print(f"{i}. {os.path.basename(file)}")
            
        try:
            choice = int(input("请输入文件编号: ")) - 1
            if 0 <= choice < len(files):
                filepath = files[choice]
                confirm = input(f"确认删除 {os.path.basename(filepath)}？(y/n): ").lower()
                if confirm == 'y':
                    os.remove(filepath)
                    print(f"✅ 文件已删除: {os.path.basename(filepath)}")
                else:
                    print("取消删除")
            else:
                print("❌ 无效的选择")
        except ValueError:
            print("❌ 请输入有效的数字")
        except Exception as e:
            print(f"❌ 删除失败: {e}")
            
    def _cleanup_debug_files(self) -> None:
        """清理调试文件"""
        patterns = ['debug_*.html', 'position_crawler_debug_*.html', 'captcha_*.png']
        count = 0
        
        for pattern in patterns:
            for file in Path('.').glob(pattern):
                try:
                    file.unlink()
                    count += 1
                except:
                    pass
                    
        print(f"✅ 清理了 {count} 个调试文件")
        
    def _cleanup_log_files(self) -> None:
        """清理日志文件"""
        count = 0
        for file in Path('.').glob('*.log'):
            try:
                file.unlink()
                count += 1
            except:
                pass
                
        print(f"✅ 清理了 {count} 个日志文件")
        
    def _cleanup_temp_files(self) -> None:
        """清理临时文件"""
        patterns = ['*.tmp', '*.temp', '__pycache__']
        count = 0
        
        for pattern in patterns:
            for file in Path('.').glob(pattern):
                try:
                    if file.is_file():
                        file.unlink()
                        count += 1
                    elif file.is_dir():
                        import shutil
                        shutil.rmtree(file)
                        count += 1
                except:
                    pass
                    
        print(f"✅ 清理了 {count} 个临时文件")
        
    def _cleanup_output_files(self) -> None:
        """清理输出文件"""
        confirm = input("确认删除所有输出文件？(y/n): ").lower()
        if confirm == 'y':
            count = 0
            for file in self.output_dir.glob('*'):
                try:
                    file.unlink()
                    count += 1
                except:
                    pass
            print(f"✅ 清理了 {count} 个输出文件")
        else:
            print("取消清理")
            
    def _cleanup_old_files(self) -> None:
        """清理旧文件"""
        days = input("请输入要清理多少天前的文件 (默认7天): ").strip()
        try:
            days = int(days) if days else 7
        except ValueError:
            days = 7
            
        cutoff_time = time.time() - (days * 24 * 3600)
        count = 0
        
        for file in self.list_output_files():
            try:
                if os.path.getmtime(file) < cutoff_time:
                    os.remove(file)
                    count += 1
            except:
                pass
                
        print(f"✅ 清理了 {count} 个 {days} 天前的文件")
        
    def _cleanup_all(self) -> None:
        """完全清理"""
        confirm = input("确认清理所有文件？这将删除所有输出、日志和临时文件 (y/n): ").lower()
        if confirm == 'y':
            self._cleanup_debug_files()
            self._cleanup_log_files()
            self._cleanup_temp_files()
            self._cleanup_output_files()
            print("✅ 项目清理完成")
        else:
            print("取消清理")
            
    def run(self) -> None:
        """运行项目管理器"""
        print("🚀 欢迎使用BOSS直聘爬虫项目管理器")
        
        while True:
            self.show_menu()
            choice = input("请选择操作: ").strip()
            
            if choice == '1':
                self.run_crawler()
            elif choice == '2':
                self.run_scheduler()
            elif choice == '3':
                self.analyze_data()
            elif choice == '4':
                self.validate_urls()
            elif choice == '5':
                self.manage_proxy()
            elif choice == '6':
                self.monitor_performance()
            elif choice == '7':
                self.manage_config()
            elif choice == '8':
                self.manage_files()
            elif choice == '9':
                self.show_project_status()
            elif choice == '10':
                self.cleanup_project()
            elif choice == '11':
                self.show_help()
            elif choice == '12':
                print("👋 感谢使用，再见！")
                break
            else:
                print("❌ 无效的选择，请重新输入")
                
            input("\n按回车键继续...")

if __name__ == "__main__":
    manager = ProjectManager()
    manager.run()
