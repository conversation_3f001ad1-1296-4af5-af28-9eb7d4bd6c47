# 📚 API文档

生成时间: 2025-07-02 15:58:51

## 📋 模块概览

- **position_crawler_core** - Position_Crawler_1项目核心逻辑实现 - 增强版
专门解决BOSS直聘反爬虫检测的核心技术
集成高级反检测、智能重试、验证码处理等功能
- **advanced_stealth** - 高级反检测模块
基于Position_Crawler_1项目的核心反检测技术
实现浏览器指纹伪造、验证码处理等高级功能
- **smart_retry** - 
- **data_analyzer** - 
- **url_validator** - URL验证工具
验证爬取的URL是否有效和可访问
- **performance_monitor** - 
- **proxy_manager** - 
- **crawler_scheduler** - 爬虫调度器
实现任务调度、并发控制、资源管理等高级功能
- **crawler_config** - 

## 📦 position_crawler_core

Position_Crawler_1项目核心逻辑实现 - 增强版
专门解决BOSS直聘反爬虫检测的核心技术
集成高级反检测、智能重试、验证码处理等功能

### 📋 类

#### `PositionCrawlerCore`

Position_Crawler_1项目的核心爬虫逻辑 - 增强版

**方法:**

- `__init__(self)`
- `get_stealth_headers(self)`
  - 获取隐蔽性请求头
- `use_seleniumbase_stealth(self, search_params)`
  - 使用SeleniumBase隐蔽模式 - Position_Crawler_1增强版核心方法
- `_build_search_url(self, params)`
  - 构造搜索URL
- `_inject_stealth_scripts(self, sb)`
  - 注入反检测脚本
- `_handle_captcha_if_present(self, sb)`
  - 检测并处理验证码
- `_handle_captcha_element(self, sb, selector)`
  - 处理具体的验证码元素
- `_handle_slider_captcha(self, sb, selector)`
  - 处理滑块验证码
- `_handle_click_captcha(self, sb, selector)`
  - 处理点击验证码
- `_handle_general_captcha(self, sb, selector)`
  - 处理通用验证码
- `_enhanced_human_simulation(self, sb)`
  - 增强的人类行为模拟
- `_smart_wait_for_content(self, sb)`
  - 智能等待页面内容加载
- `_deep_human_simulation(self, sb)`
  - 深度人类行为模拟
- `_is_verification_page(self, html_content)`
  - 检查是否为验证页面
- `_handle_verification_page(self, sb)`
  - 处理验证页面
- `_extract_urls_from_html(self, html_content)`
  - 从HTML中提取职位URL - Position_Crawler_1优化版
- `_is_valid_job_url(self, url)`
  - 验证职位URL有效性
- `_save_debug_page(self, html_content)`
  - 保存调试页面
- `crawl_boss_jobs(self, cities, keywords)`
  - 使用Position_Crawler_1技术爬取BOSS直聘职位
- `save_results(self, filename)`
  - 保存结果
- `print_statistics(self)`
  - 打印统计信息

---

## 📦 advanced_stealth

高级反检测模块
基于Position_Crawler_1项目的核心反检测技术
实现浏览器指纹伪造、验证码处理等高级功能

### 📋 类

#### `AdvancedStealth`

高级反检测技术实现

**方法:**

- `__init__(self)`
- `_generate_fingerprint_config(self)`
  - 生成浏览器指纹配置
- `get_stealth_js_scripts(self)`
  - 获取反检测JavaScript脚本
- `get_advanced_chrome_options(self)`
  - 获取高级Chrome选项
- `simulate_human_behavior(self, page)`
  - 模拟人类行为
- `get_random_delays(self)`
  - 获取随机延迟配置
- `handle_captcha_detection(self, page)`
  - 检测和处理验证码
- `_handle_slider_captcha(self, page, selector)`
  - 处理滑块验证码
- `_handle_general_captcha(self, page, selector)`
  - 处理一般验证码

---

## 📦 url_validator

URL验证工具
验证爬取的URL是否有效和可访问

### 📋 类

#### `URLValidator`

URL验证器

**方法:**

- `__init__(self, max_concurrent, timeout)`
- `analyze_results(self)`
  - 分析验证结果
- `generate_report(self)`
  - 生成验证报告
- `save_results(self, filename)`
  - 保存验证结果
- `get_valid_urls(self)`
  - 获取有效的URL列表
- `get_problem_urls(self)`
  - 获取有问题的URL

---

## 📦 crawler_scheduler

爬虫调度器
实现任务调度、并发控制、资源管理等高级功能

### 📋 类

#### `TaskStatus`

任务状态

#### `TaskPriority`

任务优先级

#### `CrawlTask`

爬取任务

**方法:**

- `age(self)`
  - 任务年龄（秒）
- `is_expired(self)`
  - 是否过期（超过1小时）
- `to_dict(self)`
  - 转换为字典

#### `CrawlerScheduler`

爬虫调度器

**方法:**

- `__init__(self, max_concurrent, enable_monitoring)`
- `add_task(self, city, keyword, page, priority)`
  - 添加任务
- `add_batch_tasks(self, cities, keywords, max_pages, priority)`
  - 批量添加任务
- `get_next_task(self)`
  - 获取下一个任务
- `start(self)`
  - 启动调度器
- `_cleanup_expired_tasks(self)`
  - 清理过期任务
- `_adjust_concurrency(self)`
  - 动态调整并发数
- `_log_status(self)`
  - 记录状态
- `_get_city_code(self, city)`
  - 获取城市代码
- `get_status_summary(self)`
  - 获取状态摘要
- `generate_report(self)`
  - 生成调度报告
- `save_results(self, filename)`
  - 保存调度结果
- `_format_duration(self, seconds)`
  - 格式化时长

---
