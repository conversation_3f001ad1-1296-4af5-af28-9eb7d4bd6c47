#!/usr/bin/env python3
"""
项目打包和分发脚本
创建可分发的项目包
"""

import os
import shutil
import zipfile
import tarfile
import time
import json
from pathlib import Path
from typing import List, Dict

class ProjectPackager:
    """项目打包器"""
    
    def __init__(self):
        self.project_root = Path.cwd()
        self.package_dir = self.project_root / "dist"
        self.package_dir.mkdir(exist_ok=True)
        
        # 项目信息
        self.project_info = {
            "name": "boss-zhipin-crawler",
            "version": "1.0.0",
            "description": "基于Position_Crawler_1核心技术的BOSS直聘爬虫系统",
            "author": "Position_Crawler_Team",
            "license": "MIT",
            "python_requires": ">=3.8"
        }
        
    def get_files_to_include(self) -> List[str]:
        """获取要包含的文件列表"""
        include_files = [
            # 核心模块
            "main.py",
            "position_crawler_core.py",
            "advanced_stealth.py",
            "smart_retry.py",
            "crawler_config.py",
            
            # 工具模块
            "data_analyzer.py",
            "url_validator.py", 
            "performance_monitor.py",
            "proxy_manager.py",
            "crawler_scheduler.py",
            
            # 管理界面
            "project_manager.py",
            "project_manager_extensions.py",
            
            # 工具脚本
            "setup.py",
            "health_check.py",
            "quick_start.py",
            "doc_generator.py",
            
            # 配置文件
            "config.py",
            "requirements.txt",
            
            # 文档
            "README.md",
            "SOLUTION_GUIDE.md",
            "PROJECT_SUMMARY.md",
            "FINAL_COMPLETION_REPORT.md",
            
            # 许可证
            "LICENSE"
        ]
        
        # 过滤存在的文件
        existing_files = []
        for file in include_files:
            if (self.project_root / file).exists():
                existing_files.append(file)
            else:
                print(f"⚠️ 文件不存在，跳过: {file}")
                
        return existing_files
        
    def get_directories_to_include(self) -> List[str]:
        """获取要包含的目录列表"""
        include_dirs = [
            "docs",  # 如果存在文档目录
        ]
        
        existing_dirs = []
        for dir_name in include_dirs:
            if (self.project_root / dir_name).exists():
                existing_dirs.append(dir_name)
                
        return existing_dirs
        
    def create_license_file(self) -> None:
        """创建许可证文件"""
        license_content = f"""MIT License

Copyright (c) {time.strftime('%Y')} Position_Crawler_Team

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
"""
        
        license_file = self.project_root / "LICENSE"
        if not license_file.exists():
            with open(license_file, 'w', encoding='utf-8') as f:
                f.write(license_content)
            print("✅ 创建许可证文件")
            
    def create_package_info(self) -> str:
        """创建包信息文件"""
        package_info = {
            **self.project_info,
            "created_at": time.strftime('%Y-%m-%d %H:%M:%S'),
            "files_included": len(self.get_files_to_include()),
            "directories_included": len(self.get_directories_to_include()),
            "installation": {
                "requirements": "pip install -r requirements.txt",
                "setup": "python setup.py",
                "quick_start": "python quick_start.py"
            },
            "features": [
                "Position_Crawler_1核心技术",
                "高级反检测技术",
                "智能重试系统",
                "数据分析工具",
                "性能监控",
                "代理管理",
                "任务调度",
                "项目管理器"
            ]
        }
        
        info_file = self.project_root / "PACKAGE_INFO.json"
        with open(info_file, 'w', encoding='utf-8') as f:
            json.dump(package_info, f, ensure_ascii=False, indent=2)
            
        return str(info_file)
        
    def create_installation_script(self) -> str:
        """创建安装脚本"""
        install_script = """#!/bin/bash
# BOSS直聘爬虫项目安装脚本

echo "🚀 开始安装BOSS直聘爬虫项目..."

# 检查Python版本
python_version=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
required_version="3.8"

if [ "$(printf '%s\\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
    echo "❌ Python版本过低，需要3.8或更高版本"
    exit 1
fi

echo "✅ Python版本检查通过: $python_version"

# 安装依赖
echo "📦 安装项目依赖..."
pip3 install -r requirements.txt

# 运行设置脚本
echo "⚙️ 运行项目设置..."
python3 setup.py

# 运行健康检查
echo "🔍 运行健康检查..."
python3 health_check.py

echo "🎉 安装完成！"
echo "📋 快速开始:"
echo "   python3 quick_start.py"
echo "   python3 project_manager.py"
"""
        
        script_file = self.project_root / "install.sh"
        with open(script_file, 'w', encoding='utf-8') as f:
            f.write(install_script)
            
        # 设置执行权限
        os.chmod(script_file, 0o755)
        
        return str(script_file)
        
    def create_zip_package(self) -> str:
        """创建ZIP包"""
        timestamp = time.strftime('%Y%m%d_%H%M%S')
        zip_name = f"{self.project_info['name']}-{self.project_info['version']}-{timestamp}.zip"
        zip_path = self.package_dir / zip_name
        
        print(f"📦 创建ZIP包: {zip_name}")
        
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # 添加文件
            for file in self.get_files_to_include():
                file_path = self.project_root / file
                if file_path.exists():
                    zipf.write(file_path, file)
                    
            # 添加目录
            for dir_name in self.get_directories_to_include():
                dir_path = self.project_root / dir_name
                for file_path in dir_path.rglob('*'):
                    if file_path.is_file():
                        arcname = str(file_path.relative_to(self.project_root))
                        zipf.write(file_path, arcname)
                        
            # 添加包信息
            package_info_file = self.create_package_info()
            zipf.write(package_info_file, "PACKAGE_INFO.json")
            
            # 添加安装脚本
            install_script = self.create_installation_script()
            zipf.write(install_script, "install.sh")
            
        print(f"✅ ZIP包创建完成: {zip_path}")
        return str(zip_path)
        
    def create_tar_package(self) -> str:
        """创建TAR.GZ包"""
        timestamp = time.strftime('%Y%m%d_%H%M%S')
        tar_name = f"{self.project_info['name']}-{self.project_info['version']}-{timestamp}.tar.gz"
        tar_path = self.package_dir / tar_name
        
        print(f"📦 创建TAR.GZ包: {tar_name}")
        
        with tarfile.open(tar_path, 'w:gz') as tarf:
            # 添加文件
            for file in self.get_files_to_include():
                file_path = self.project_root / file
                if file_path.exists():
                    tarf.add(file_path, arcname=file)
                    
            # 添加目录
            for dir_name in self.get_directories_to_include():
                dir_path = self.project_root / dir_name
                if dir_path.exists():
                    tarf.add(dir_path, arcname=dir_name)
                    
            # 添加包信息
            package_info_file = self.create_package_info()
            tarf.add(package_info_file, arcname="PACKAGE_INFO.json")
            
            # 添加安装脚本
            install_script = self.create_installation_script()
            tarf.add(install_script, arcname="install.sh")
            
        print(f"✅ TAR.GZ包创建完成: {tar_path}")
        return str(tar_path)
        
    def create_source_package(self) -> str:
        """创建源码包"""
        timestamp = time.strftime('%Y%m%d_%H%M%S')
        source_dir = self.package_dir / f"{self.project_info['name']}-source-{timestamp}"
        
        print(f"📦 创建源码包: {source_dir.name}")
        
        # 创建源码目录
        source_dir.mkdir(exist_ok=True)
        
        # 复制文件
        for file in self.get_files_to_include():
            file_path = self.project_root / file
            if file_path.exists():
                shutil.copy2(file_path, source_dir / file)
                
        # 复制目录
        for dir_name in self.get_directories_to_include():
            dir_path = self.project_root / dir_name
            if dir_path.exists():
                shutil.copytree(dir_path, source_dir / dir_name, dirs_exist_ok=True)
                
        # 创建输出目录
        (source_dir / "output").mkdir(exist_ok=True)
        
        # 添加包信息
        package_info_file = self.create_package_info()
        shutil.copy2(package_info_file, source_dir / "PACKAGE_INFO.json")
        
        # 添加安装脚本
        install_script = self.create_installation_script()
        shutil.copy2(install_script, source_dir / "install.sh")
        
        # 创建README
        readme_content = f"""# {self.project_info['name']}

{self.project_info['description']}

## 快速安装

### 方法一：使用安装脚本
```bash
chmod +x install.sh
./install.sh
```

### 方法二：手动安装
```bash
# 安装依赖
pip install -r requirements.txt

# 运行设置
python setup.py

# 快速开始
python quick_start.py
```

## 使用说明

详细使用说明请参考：
- README.md - 项目概述
- docs/USAGE_GUIDE.md - 使用指南
- docs/API_REFERENCE.md - API文档

## 技术支持

如遇问题请：
1. 运行健康检查：`python health_check.py`
2. 查看日志文件
3. 参考文档说明

版本: {self.project_info['version']}
创建时间: {time.strftime('%Y-%m-%d %H:%M:%S')}
"""
        
        with open(source_dir / "INSTALL.md", 'w', encoding='utf-8') as f:
            f.write(readme_content)
            
        print(f"✅ 源码包创建完成: {source_dir}")
        return str(source_dir)
        
    def package_all(self) -> Dict[str, str]:
        """创建所有包格式"""
        print("📦 开始打包项目...")
        
        # 创建许可证文件
        self.create_license_file()
        
        packages = {}
        
        try:
            # 创建ZIP包
            zip_package = self.create_zip_package()
            packages['zip'] = zip_package
            
            # 创建TAR.GZ包
            tar_package = self.create_tar_package()
            packages['tar'] = tar_package
            
            # 创建源码包
            source_package = self.create_source_package()
            packages['source'] = source_package
            
            print(f"\n🎉 打包完成！共创建 {len(packages)} 个包")
            
        except Exception as e:
            print(f"❌ 打包失败: {e}")
            
        return packages
        
    def generate_release_notes(self, packages: Dict[str, str]) -> str:
        """生成发布说明"""
        release_notes = f"""# 🎉 {self.project_info['name']} v{self.project_info['version']} 发布

发布时间: {time.strftime('%Y-%m-%d %H:%M:%S')}

## 📦 下载

"""
        
        for package_type, package_path in packages.items():
            package_name = Path(package_path).name
            if package_type == 'source':
                release_notes += f"- **源码包**: `{package_name}/`\n"
            else:
                release_notes += f"- **{package_type.upper()}包**: `{package_name}`\n"
                
        release_notes += f"""

## 🚀 快速开始

### 下载并解压
```bash
# 下载ZIP包
unzip {self.project_info['name']}-{self.project_info['version']}-*.zip

# 或下载TAR.GZ包  
tar -xzf {self.project_info['name']}-{self.project_info['version']}-*.tar.gz
```

### 安装和运行
```bash
# 进入项目目录
cd {self.project_info['name']}-*

# 运行安装脚本
chmod +x install.sh
./install.sh

# 或手动安装
pip install -r requirements.txt
python setup.py

# 快速开始
python quick_start.py
```

## ✨ 主要功能

- 🕷️ Position_Crawler_1核心技术
- 🛡️ 高级反检测技术
- 🔄 智能重试系统
- 📊 数据分析工具
- 📈 性能监控
- 🌐 代理管理
- 📅 任务调度
- 🎮 项目管理器

## 📋 系统要求

- Python 3.8+
- 支持的操作系统：Windows, macOS, Linux
- 内存：建议2GB以上
- 磁盘空间：建议1GB以上

## 📖 文档

- README.md - 项目概述
- SOLUTION_GUIDE.md - 解决方案指南
- docs/USAGE_GUIDE.md - 使用指南
- docs/API_REFERENCE.md - API文档

## ⚠️ 重要说明

- 本项目仅供学习和研究使用
- 请遵守目标网站的使用条款
- BOSS直聘具有严格的反爬虫机制
- 建议使用Position_Crawler_1项目的完整解决方案

## 🆘 技术支持

如遇问题请：
1. 运行健康检查：`python health_check.py`
2. 查看日志文件和错误信息
3. 参考文档和解决方案指南

---

感谢使用 {self.project_info['name']}！
"""

        # 保存发布说明
        release_file = self.package_dir / f"RELEASE_NOTES_v{self.project_info['version']}.md"
        with open(release_file, 'w', encoding='utf-8') as f:
            f.write(release_notes)
            
        print(f"📝 发布说明已生成: {release_file}")
        return str(release_file)

def main():
    """主函数"""
    packager = ProjectPackager()
    
    print("📦 BOSS直聘爬虫项目打包工具")
    print("=" * 50)
    
    # 确认打包
    confirm = input("是否开始打包项目？(y/n): ").lower()
    if confirm != 'y':
        print("打包已取消")
        return
        
    # 执行打包
    packages = packager.package_all()
    
    if packages:
        # 生成发布说明
        release_notes = packager.generate_release_notes(packages)
        
        print("\n📦 打包结果:")
        for package_type, package_path in packages.items():
            print(f"   {package_type.upper()}: {package_path}")
            
        print(f"\n📝 发布说明: {release_notes}")
        print("\n🎉 项目打包完成！")
    else:
        print("❌ 打包失败")

if __name__ == "__main__":
    main()
