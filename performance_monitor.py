#!/usr/bin/env python3
"""
性能监控模块
监控爬虫运行性能、资源使用情况和系统状态
"""

import time
import psutil
import threading
import json
import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import os

logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetrics:
    """性能指标数据类"""
    timestamp: float
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    disk_io_read_mb: float
    disk_io_write_mb: float
    network_sent_mb: float
    network_recv_mb: float
    active_threads: int
    open_files: int

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, interval: float = 5.0, max_records: int = 1000):
        self.interval = interval
        self.max_records = max_records
        self.metrics_history: List[PerformanceMetrics] = []
        self.is_monitoring = False
        self.monitor_thread: Optional[threading.Thread] = None
        self.start_time = None
        self.process = psutil.Process()
        
        # 初始化基准值
        self.baseline_disk_io = psutil.disk_io_counters()
        self.baseline_network = psutil.net_io_counters()
        
    def start_monitoring(self) -> None:
        """开始监控"""
        if self.is_monitoring:
            logger.warning("性能监控已在运行")
            return
            
        self.is_monitoring = True
        self.start_time = time.time()
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        logger.info("🔍 性能监控已启动")
        
    def stop_monitoring(self) -> None:
        """停止监控"""
        if not self.is_monitoring:
            return
            
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=2)
        logger.info("⏹️ 性能监控已停止")
        
    def _monitor_loop(self) -> None:
        """监控循环"""
        while self.is_monitoring:
            try:
                metrics = self._collect_metrics()
                self.metrics_history.append(metrics)
                
                # 限制历史记录数量
                if len(self.metrics_history) > self.max_records:
                    self.metrics_history = self.metrics_history[-self.max_records:]
                    
                time.sleep(self.interval)
                
            except Exception as e:
                logger.error(f"性能监控异常: {e}")
                time.sleep(self.interval)
                
    def _collect_metrics(self) -> PerformanceMetrics:
        """收集性能指标"""
        try:
            # CPU和内存
            cpu_percent = self.process.cpu_percent()
            memory_info = self.process.memory_info()
            memory_percent = self.process.memory_percent()
            memory_used_mb = memory_info.rss / 1024 / 1024
            
            # 磁盘IO
            current_disk_io = psutil.disk_io_counters()
            if current_disk_io and self.baseline_disk_io:
                disk_read_mb = (current_disk_io.read_bytes - self.baseline_disk_io.read_bytes) / 1024 / 1024
                disk_write_mb = (current_disk_io.write_bytes - self.baseline_disk_io.write_bytes) / 1024 / 1024
            else:
                disk_read_mb = disk_write_mb = 0
                
            # 网络IO
            current_network = psutil.net_io_counters()
            if current_network and self.baseline_network:
                network_sent_mb = (current_network.bytes_sent - self.baseline_network.bytes_sent) / 1024 / 1024
                network_recv_mb = (current_network.bytes_recv - self.baseline_network.bytes_recv) / 1024 / 1024
            else:
                network_sent_mb = network_recv_mb = 0
                
            # 线程和文件
            active_threads = self.process.num_threads()
            open_files = len(self.process.open_files())
            
            return PerformanceMetrics(
                timestamp=time.time(),
                cpu_percent=cpu_percent,
                memory_percent=memory_percent,
                memory_used_mb=memory_used_mb,
                disk_io_read_mb=disk_read_mb,
                disk_io_write_mb=disk_write_mb,
                network_sent_mb=network_sent_mb,
                network_recv_mb=network_recv_mb,
                active_threads=active_threads,
                open_files=open_files
            )
            
        except Exception as e:
            logger.error(f"收集性能指标失败: {e}")
            return PerformanceMetrics(
                timestamp=time.time(),
                cpu_percent=0, memory_percent=0, memory_used_mb=0,
                disk_io_read_mb=0, disk_io_write_mb=0,
                network_sent_mb=0, network_recv_mb=0,
                active_threads=0, open_files=0
            )
            
    def get_current_status(self) -> Dict[str, Any]:
        """获取当前状态"""
        if not self.metrics_history:
            return {"status": "no_data", "message": "暂无监控数据"}
            
        latest = self.metrics_history[-1]
        runtime = time.time() - self.start_time if self.start_time else 0
        
        return {
            "status": "monitoring" if self.is_monitoring else "stopped",
            "runtime_seconds": runtime,
            "runtime_formatted": self._format_duration(runtime),
            "current_metrics": asdict(latest),
            "total_records": len(self.metrics_history),
            "monitoring_interval": self.interval
        }
        
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        if not self.metrics_history:
            return {"error": "暂无监控数据"}
            
        # 计算统计信息
        cpu_values = [m.cpu_percent for m in self.metrics_history]
        memory_values = [m.memory_used_mb for m in self.metrics_history]
        
        summary = {
            "monitoring_period": {
                "start_time": datetime.fromtimestamp(self.metrics_history[0].timestamp).isoformat(),
                "end_time": datetime.fromtimestamp(self.metrics_history[-1].timestamp).isoformat(),
                "duration_seconds": self.metrics_history[-1].timestamp - self.metrics_history[0].timestamp,
                "total_records": len(self.metrics_history)
            },
            "cpu_usage": {
                "avg": sum(cpu_values) / len(cpu_values),
                "max": max(cpu_values),
                "min": min(cpu_values),
                "current": cpu_values[-1]
            },
            "memory_usage": {
                "avg_mb": sum(memory_values) / len(memory_values),
                "max_mb": max(memory_values),
                "min_mb": min(memory_values),
                "current_mb": memory_values[-1]
            },
            "resource_peaks": self._find_resource_peaks(),
            "performance_trends": self._analyze_trends()
        }
        
        return summary
        
    def _find_resource_peaks(self) -> Dict[str, Any]:
        """查找资源使用峰值"""
        if not self.metrics_history:
            return {}
            
        max_cpu_metric = max(self.metrics_history, key=lambda m: m.cpu_percent)
        max_memory_metric = max(self.metrics_history, key=lambda m: m.memory_used_mb)
        
        return {
            "max_cpu": {
                "value": max_cpu_metric.cpu_percent,
                "timestamp": datetime.fromtimestamp(max_cpu_metric.timestamp).isoformat()
            },
            "max_memory": {
                "value_mb": max_memory_metric.memory_used_mb,
                "timestamp": datetime.fromtimestamp(max_memory_metric.timestamp).isoformat()
            }
        }
        
    def _analyze_trends(self) -> Dict[str, str]:
        """分析性能趋势"""
        if len(self.metrics_history) < 10:
            return {"message": "数据不足，无法分析趋势"}
            
        # 取最近的数据进行趋势分析
        recent_data = self.metrics_history[-10:]
        early_data = self.metrics_history[:10]
        
        recent_cpu_avg = sum(m.cpu_percent for m in recent_data) / len(recent_data)
        early_cpu_avg = sum(m.cpu_percent for m in early_data) / len(early_data)
        
        recent_memory_avg = sum(m.memory_used_mb for m in recent_data) / len(recent_data)
        early_memory_avg = sum(m.memory_used_mb for m in early_data) / len(early_data)
        
        cpu_trend = "上升" if recent_cpu_avg > early_cpu_avg * 1.1 else "下降" if recent_cpu_avg < early_cpu_avg * 0.9 else "稳定"
        memory_trend = "上升" if recent_memory_avg > early_memory_avg * 1.1 else "下降" if recent_memory_avg < early_memory_avg * 0.9 else "稳定"
        
        return {
            "cpu_trend": cpu_trend,
            "memory_trend": memory_trend,
            "analysis": f"CPU使用率呈{cpu_trend}趋势，内存使用呈{memory_trend}趋势"
        }
        
    def generate_report(self) -> str:
        """生成性能报告"""
        if not self.metrics_history:
            return "暂无监控数据"
            
        summary = self.get_performance_summary()
        current = self.get_current_status()
        
        report_lines = []
        report_lines.append("=" * 80)
        report_lines.append("📊 性能监控报告")
        report_lines.append("=" * 80)
        report_lines.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append("")
        
        # 监控概况
        period = summary.get("monitoring_period", {})
        report_lines.append("📈 监控概况")
        report_lines.append("-" * 40)
        report_lines.append(f"监控时长: {self._format_duration(period.get('duration_seconds', 0))}")
        report_lines.append(f"数据点数: {period.get('total_records', 0)}")
        report_lines.append(f"采样间隔: {self.interval}秒")
        report_lines.append("")
        
        # CPU使用情况
        cpu = summary.get("cpu_usage", {})
        report_lines.append("🖥️ CPU使用情况")
        report_lines.append("-" * 40)
        report_lines.append(f"平均使用率: {cpu.get('avg', 0):.1f}%")
        report_lines.append(f"峰值使用率: {cpu.get('max', 0):.1f}%")
        report_lines.append(f"当前使用率: {cpu.get('current', 0):.1f}%")
        report_lines.append("")
        
        # 内存使用情况
        memory = summary.get("memory_usage", {})
        report_lines.append("💾 内存使用情况")
        report_lines.append("-" * 40)
        report_lines.append(f"平均使用: {memory.get('avg_mb', 0):.1f}MB")
        report_lines.append(f"峰值使用: {memory.get('max_mb', 0):.1f}MB")
        report_lines.append(f"当前使用: {memory.get('current_mb', 0):.1f}MB")
        report_lines.append("")
        
        # 性能趋势
        trends = summary.get("performance_trends", {})
        if trends.get("analysis"):
            report_lines.append("📈 性能趋势")
            report_lines.append("-" * 40)
            report_lines.append(trends["analysis"])
            report_lines.append("")
            
        # 资源峰值
        peaks = summary.get("resource_peaks", {})
        if peaks:
            report_lines.append("⚡ 资源峰值")
            report_lines.append("-" * 40)
            if "max_cpu" in peaks:
                report_lines.append(f"CPU峰值: {peaks['max_cpu']['value']:.1f}%")
            if "max_memory" in peaks:
                report_lines.append(f"内存峰值: {peaks['max_memory']['value_mb']:.1f}MB")
            report_lines.append("")
            
        # 性能建议
        recommendations = self._get_performance_recommendations(summary)
        if recommendations:
            report_lines.append("💡 性能建议")
            report_lines.append("-" * 40)
            for rec in recommendations:
                report_lines.append(f"• {rec}")
            report_lines.append("")
            
        report_lines.append("=" * 80)
        
        return "\n".join(report_lines)
        
    def _get_performance_recommendations(self, summary: Dict[str, Any]) -> List[str]:
        """获取性能优化建议"""
        recommendations = []
        
        cpu = summary.get("cpu_usage", {})
        memory = summary.get("memory_usage", {})
        trends = summary.get("performance_trends", {})
        
        # CPU建议
        if cpu.get("avg", 0) > 80:
            recommendations.append("CPU使用率较高，建议减少并发数或增加延迟时间")
        elif cpu.get("max", 0) > 95:
            recommendations.append("检测到CPU峰值过高，建议优化算法或分批处理")
            
        # 内存建议
        if memory.get("avg_mb", 0) > 1000:
            recommendations.append("内存使用较高，建议优化数据结构或增加垃圾回收")
        elif memory.get("max_mb", 0) > 2000:
            recommendations.append("内存峰值过高，建议检查内存泄漏或分批处理数据")
            
        # 趋势建议
        if trends.get("cpu_trend") == "上升":
            recommendations.append("CPU使用率呈上升趋势，建议监控系统负载")
        if trends.get("memory_trend") == "上升":
            recommendations.append("内存使用呈上升趋势，建议检查内存管理")
            
        if not recommendations:
            recommendations.append("系统性能表现良好，继续保持当前配置")
            
        return recommendations
        
    def save_metrics(self, filename: str = None) -> str:
        """保存监控数据"""
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"performance_metrics_{timestamp}.json"
            
        try:
            data = {
                "metadata": {
                    "generated_at": datetime.now().isoformat(),
                    "monitoring_interval": self.interval,
                    "total_records": len(self.metrics_history),
                    "duration_seconds": self.metrics_history[-1].timestamp - self.metrics_history[0].timestamp if self.metrics_history else 0
                },
                "summary": self.get_performance_summary(),
                "metrics": [asdict(m) for m in self.metrics_history]
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
            logger.info(f"📊 性能数据已保存到: {filename}")
            return filename
            
        except Exception as e:
            logger.error(f"保存性能数据失败: {e}")
            return ""
            
    def _format_duration(self, seconds: float) -> str:
        """格式化时长"""
        if seconds < 60:
            return f"{seconds:.1f}秒"
        elif seconds < 3600:
            return f"{seconds/60:.1f}分钟"
        else:
            return f"{seconds/3600:.1f}小时"
            
    def __enter__(self):
        """上下文管理器入口"""
        self.start_monitoring()
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.stop_monitoring()

# 全局监控器实例
global_monitor = PerformanceMonitor()

def start_global_monitoring():
    """启动全局监控"""
    global_monitor.start_monitoring()

def stop_global_monitoring():
    """停止全局监控"""
    global_monitor.stop_monitoring()

def get_performance_status():
    """获取性能状态"""
    return global_monitor.get_current_status()

def generate_performance_report():
    """生成性能报告"""
    return global_monitor.generate_report()

if __name__ == "__main__":
    # 测试性能监控
    print("🔍 启动性能监控测试...")
    
    with PerformanceMonitor(interval=1.0) as monitor:
        # 模拟一些工作负载
        import time
        for i in range(10):
            print(f"工作负载 {i+1}/10")
            time.sleep(1)
            
        # 生成报告
        report = monitor.generate_report()
        print(report)
        
        # 保存数据
        filename = monitor.save_metrics()
        print(f"数据已保存到: {filename}")
