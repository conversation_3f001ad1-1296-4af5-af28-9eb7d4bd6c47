#!/usr/bin/env python3
"""
项目完成验证脚本
验证项目的完整性和功能性
"""

import os
import sys
import time
import json
import importlib
from pathlib import Path
from typing import Dict, List, Any, Tuple

class ProjectVerifier:
    """项目验证器"""
    
    def __init__(self):
        self.project_root = Path.cwd()
        self.verification_results = {}
        
    def verify_file_structure(self) -> Tuple[bool, Dict[str, Any]]:
        """验证文件结构"""
        print("📁 验证项目文件结构...")
        
        required_files = {
            "核心模块": [
                "main.py",
                "position_crawler_core.py", 
                "advanced_stealth.py",
                "smart_retry.py",
                "crawler_config.py"
            ],
            "工具模块": [
                "data_analyzer.py",
                "url_validator.py",
                "performance_monitor.py", 
                "proxy_manager.py",
                "crawler_scheduler.py"
            ],
            "管理界面": [
                "project_manager.py",
                "project_manager_extensions.py"
            ],
            "工具脚本": [
                "setup.py",
                "health_check.py",
                "quick_start.py",
                "doc_generator.py",
                "package_project.py"
            ],
            "配置文件": [
                "config.py",
                "requirements.txt"
            ],
            "文档": [
                "README.md",
                "SOLUTION_GUIDE.md",
                "PROJECT_SUMMARY.md",
                "FINAL_COMPLETION_REPORT.md"
            ]
        }
        
        results = {}
        all_passed = True
        
        for category, files in required_files.items():
            category_results = {
                "total": len(files),
                "existing": 0,
                "missing": []
            }
            
            for file in files:
                if (self.project_root / file).exists():
                    category_results["existing"] += 1
                else:
                    category_results["missing"].append(file)
                    all_passed = False
                    
            category_results["completion_rate"] = category_results["existing"] / category_results["total"] * 100
            results[category] = category_results
            
        return all_passed, results
        
    def verify_module_imports(self) -> Tuple[bool, Dict[str, Any]]:
        """验证模块导入"""
        print("📦 验证模块导入...")
        
        modules_to_test = [
            "position_crawler_core",
            "advanced_stealth",
            "smart_retry", 
            "data_analyzer",
            "url_validator",
            "performance_monitor",
            "proxy_manager",
            "crawler_scheduler",
            "crawler_config",
            "project_manager"
        ]
        
        results = {
            "total": len(modules_to_test),
            "successful": 0,
            "failed": [],
            "success_details": []
        }
        
        for module_name in modules_to_test:
            try:
                module = importlib.import_module(module_name)
                results["successful"] += 1
                results["success_details"].append({
                    "module": module_name,
                    "file": getattr(module, "__file__", "unknown"),
                    "docstring": getattr(module, "__doc__", "")[:100] if getattr(module, "__doc__", "") else ""
                })
            except Exception as e:
                results["failed"].append({
                    "module": module_name,
                    "error": str(e)
                })
                
        results["success_rate"] = results["successful"] / results["total"] * 100
        all_passed = results["successful"] == results["total"]
        
        return all_passed, results
        
    def verify_dependencies(self) -> Tuple[bool, Dict[str, Any]]:
        """验证依赖"""
        print("🔗 验证项目依赖...")
        
        critical_dependencies = [
            "bs4",  # beautifulsoup4的导入名
            "lxml",
            "aiohttp",
            "requests",
            "seleniumbase",
            "playwright",
            "cloudscraper",
            "psutil"
        ]
        
        results = {
            "total": len(critical_dependencies),
            "available": 0,
            "missing": [],
            "available_details": []
        }
        
        for dep in critical_dependencies:
            try:
                module = importlib.import_module(dep.replace('-', '_'))
                results["available"] += 1
                
                # 尝试获取版本信息
                version = "unknown"
                if hasattr(module, "__version__"):
                    version = module.__version__
                elif hasattr(module, "version"):
                    version = module.version
                    
                results["available_details"].append({
                    "name": dep,
                    "version": version
                })
            except ImportError:
                results["missing"].append(dep)
                
        results["availability_rate"] = results["available"] / results["total"] * 100
        all_passed = results["available"] == results["total"]
        
        return all_passed, results
        
    def verify_configuration(self) -> Tuple[bool, Dict[str, Any]]:
        """验证配置"""
        print("⚙️ 验证项目配置...")
        
        results = {
            "config_file_exists": False,
            "config_loadable": False,
            "required_keys_present": False,
            "config_details": {}
        }
        
        # 检查配置文件
        config_file = self.project_root / "crawler_config.json"
        if config_file.exists():
            results["config_file_exists"] = True
            
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                results["config_loadable"] = True
                results["config_details"]["keys_count"] = len(config_data.keys())
                
                # 检查必需的配置项
                required_keys = ["base_url", "delay_min", "delay_max", "target_cities", "target_keywords"]
                missing_keys = [key for key in required_keys if key not in config_data]
                
                if not missing_keys:
                    results["required_keys_present"] = True
                else:
                    results["config_details"]["missing_keys"] = missing_keys
                    
            except Exception as e:
                results["config_details"]["load_error"] = str(e)
                
        # 检查配置模块
        try:
            from crawler_config import get_config
            config = get_config()
            results["config_module_working"] = True
            results["config_details"]["module_config_keys"] = len(config.__dict__)
        except Exception as e:
            results["config_module_working"] = False
            results["config_details"]["module_error"] = str(e)
            
        all_passed = all([
            results["config_file_exists"],
            results["config_loadable"], 
            results["required_keys_present"],
            results.get("config_module_working", False)
        ])
        
        return all_passed, results
        
    def verify_functionality(self) -> Tuple[bool, Dict[str, Any]]:
        """验证功能性"""
        print("🧪 验证核心功能...")
        
        results = {
            "tests_run": 0,
            "tests_passed": 0,
            "test_details": []
        }
        
        # 测试1：配置加载
        try:
            from crawler_config import get_config
            config = get_config()
            results["tests_run"] += 1
            results["tests_passed"] += 1
            results["test_details"].append({
                "test": "配置加载",
                "status": "passed",
                "details": f"成功加载配置，包含{len(config.__dict__)}个配置项"
            })
        except Exception as e:
            results["tests_run"] += 1
            results["test_details"].append({
                "test": "配置加载",
                "status": "failed", 
                "error": str(e)
            })
            
        # 测试2：数据分析器
        try:
            from data_analyzer import URLAnalyzer
            analyzer = URLAnalyzer()
            results["tests_run"] += 1
            results["tests_passed"] += 1
            results["test_details"].append({
                "test": "数据分析器",
                "status": "passed",
                "details": "URLAnalyzer实例化成功"
            })
        except Exception as e:
            results["tests_run"] += 1
            results["test_details"].append({
                "test": "数据分析器",
                "status": "failed",
                "error": str(e)
            })
            
        # 测试3：性能监控器
        try:
            from performance_monitor import PerformanceMonitor
            monitor = PerformanceMonitor()
            results["tests_run"] += 1
            results["tests_passed"] += 1
            results["test_details"].append({
                "test": "性能监控器",
                "status": "passed",
                "details": "PerformanceMonitor实例化成功"
            })
        except Exception as e:
            results["tests_run"] += 1
            results["test_details"].append({
                "test": "性能监控器",
                "status": "failed",
                "error": str(e)
            })
            
        # 测试4：代理管理器
        try:
            from proxy_manager import ProxyManager
            proxy_mgr = ProxyManager()
            results["tests_run"] += 1
            results["tests_passed"] += 1
            results["test_details"].append({
                "test": "代理管理器",
                "status": "passed",
                "details": "ProxyManager实例化成功"
            })
        except Exception as e:
            results["tests_run"] += 1
            results["test_details"].append({
                "test": "代理管理器",
                "status": "failed",
                "error": str(e)
            })
            
        results["success_rate"] = results["tests_passed"] / results["tests_run"] * 100 if results["tests_run"] > 0 else 0
        all_passed = results["tests_passed"] == results["tests_run"]
        
        return all_passed, results
        
    def run_full_verification(self) -> Dict[str, Any]:
        """运行完整验证"""
        print("🔍 开始项目完整性验证...")
        print("=" * 60)
        
        verification_results = {
            "timestamp": time.time(),
            "overall_status": "unknown",
            "verifications": {}
        }
        
        # 运行各项验证
        verifications = [
            ("文件结构", self.verify_file_structure),
            ("模块导入", self.verify_module_imports),
            ("项目依赖", self.verify_dependencies),
            ("配置系统", self.verify_configuration),
            ("核心功能", self.verify_functionality)
        ]
        
        all_passed = True
        
        for name, verify_func in verifications:
            print(f"\n🔍 验证{name}...")
            try:
                passed, results = verify_func()
                verification_results["verifications"][name] = {
                    "passed": passed,
                    "results": results
                }
                
                if passed:
                    print(f"✅ {name}验证通过")
                else:
                    print(f"❌ {name}验证失败")
                    all_passed = False
                    
            except Exception as e:
                print(f"❌ {name}验证异常: {e}")
                verification_results["verifications"][name] = {
                    "passed": False,
                    "error": str(e)
                }
                all_passed = False
                
        # 设置总体状态
        verification_results["overall_status"] = "passed" if all_passed else "failed"
        
        return verification_results
        
    def generate_verification_report(self, results: Dict[str, Any]) -> str:
        """生成验证报告"""
        report_lines = []
        report_lines.append("=" * 80)
        report_lines.append("🔍 项目完整性验证报告")
        report_lines.append("=" * 80)
        report_lines.append(f"验证时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 总体状态
        status_icon = "✅" if results["overall_status"] == "passed" else "❌"
        report_lines.append(f"总体状态: {status_icon} {results['overall_status'].upper()}")
        report_lines.append("")
        
        # 详细验证结果
        for name, verification in results["verifications"].items():
            icon = "✅" if verification["passed"] else "❌"
            report_lines.append(f"{icon} {name}")
            
            if "error" in verification:
                report_lines.append(f"   错误: {verification['error']}")
            elif "results" in verification:
                result_data = verification["results"]
                
                # 根据验证类型显示不同的详细信息
                if name == "文件结构":
                    for category, data in result_data.items():
                        report_lines.append(f"   {category}: {data['existing']}/{data['total']} ({data['completion_rate']:.1f}%)")
                        if data['missing']:
                            report_lines.append(f"     缺失: {', '.join(data['missing'])}")
                            
                elif name == "模块导入":
                    report_lines.append(f"   成功导入: {result_data['successful']}/{result_data['total']} ({result_data['success_rate']:.1f}%)")
                    if result_data['failed']:
                        for failed in result_data['failed']:
                            report_lines.append(f"     失败: {failed['module']} - {failed['error']}")
                            
                elif name == "项目依赖":
                    report_lines.append(f"   可用依赖: {result_data['available']}/{result_data['total']} ({result_data['availability_rate']:.1f}%)")
                    if result_data['missing']:
                        report_lines.append(f"     缺失: {', '.join(result_data['missing'])}")
                        
                elif name == "核心功能":
                    report_lines.append(f"   测试通过: {result_data['tests_passed']}/{result_data['tests_run']} ({result_data['success_rate']:.1f}%)")
                    
            report_lines.append("")
            
        # 总结和建议
        report_lines.append("💡 总结:")
        if results["overall_status"] == "passed":
            report_lines.append("   🎉 项目验证完全通过，所有功能正常！")
            report_lines.append("   📋 项目已准备就绪，可以正常使用")
        else:
            report_lines.append("   ⚠️ 项目验证发现问题，建议修复后使用")
            report_lines.append("   🔧 请根据上述详细信息进行修复")
            
        report_lines.append("")
        report_lines.append("=" * 80)
        
        return "\n".join(report_lines)
        
    def save_verification_results(self, results: Dict[str, Any]) -> str:
        """保存验证结果"""
        timestamp = time.strftime('%Y%m%d_%H%M%S')
        filename = f"project_verification_{timestamp}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            return filename
        except Exception as e:
            print(f"❌ 保存验证结果失败: {e}")
            return ""

def main():
    """主函数"""
    print("🔍 BOSS直聘爬虫项目完整性验证")
    print("=" * 50)
    
    verifier = ProjectVerifier()
    
    # 运行验证
    results = verifier.run_full_verification()
    
    # 生成报告
    report = verifier.generate_verification_report(results)
    print("\n" + report)
    
    # 保存结果
    result_file = verifier.save_verification_results(results)
    if result_file:
        print(f"📄 详细验证结果已保存到: {result_file}")
        
    # 返回状态码
    if results["overall_status"] == "passed":
        print("\n🎉 项目验证完全通过！")
        sys.exit(0)
    else:
        print("\n⚠️ 项目验证发现问题，请修复后重新验证")
        sys.exit(1)

if __name__ == "__main__":
    main()
