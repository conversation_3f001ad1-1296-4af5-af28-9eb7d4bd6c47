#!/usr/bin/env python3
"""
智能重试和错误处理模块
基于Position_Crawler_1项目的智能重试策略
实现自适应延迟、错误分类、智能恢复等功能
"""

import time
import random
import logging
from typing import Dict, List, Optional, Callable, Any
from enum import Enum
from dataclasses import dataclass

logger = logging.getLogger(__name__)

class ErrorType(Enum):
    """错误类型枚举"""
    NETWORK_ERROR = "network_error"
    CAPTCHA_ERROR = "captcha_error"
    IP_BLOCKED = "ip_blocked"
    RATE_LIMITED = "rate_limited"
    PAGE_NOT_FOUND = "page_not_found"
    TIMEOUT_ERROR = "timeout_error"
    UNKNOWN_ERROR = "unknown_error"

@dataclass
class RetryConfig:
    """重试配置"""
    max_retries: int = 5
    base_delay: float = 2.0
    max_delay: float = 60.0
    backoff_factor: float = 2.0
    jitter: bool = True

class SmartRetry:
    """智能重试管理器"""
    
    def __init__(self):
        self.error_patterns = self._init_error_patterns()
        self.retry_configs = self._init_retry_configs()
        self.error_history = []
        
    def _init_error_patterns(self) -> Dict[ErrorType, List[str]]:
        """初始化错误模式"""
        return {
            ErrorType.IP_BLOCKED: [
                "ip地址存在异常行为",
                "ip异常",
                "访问频率过高",
                "请求过于频繁",
                "blocked",
                "forbidden"
            ],
            ErrorType.CAPTCHA_ERROR: [
                "验证码",
                "captcha",
                "verification",
                "人机验证",
                "滑块验证",
                "点击验证"
            ],
            ErrorType.RATE_LIMITED: [
                "rate limit",
                "too many requests",
                "请求过多",
                "访问限制",
                "频率限制"
            ],
            ErrorType.NETWORK_ERROR: [
                "connection error",
                "network error",
                "timeout",
                "连接超时",
                "网络错误",
                "connection refused"
            ],
            ErrorType.PAGE_NOT_FOUND: [
                "404",
                "not found",
                "页面不存在",
                "找不到页面"
            ]
        }
        
    def _init_retry_configs(self) -> Dict[ErrorType, RetryConfig]:
        """初始化重试配置"""
        return {
            ErrorType.NETWORK_ERROR: RetryConfig(
                max_retries=3,
                base_delay=1.0,
                max_delay=10.0,
                backoff_factor=1.5
            ),
            ErrorType.RATE_LIMITED: RetryConfig(
                max_retries=5,
                base_delay=10.0,
                max_delay=120.0,
                backoff_factor=2.0
            ),
            ErrorType.IP_BLOCKED: RetryConfig(
                max_retries=2,
                base_delay=30.0,
                max_delay=300.0,
                backoff_factor=3.0
            ),
            ErrorType.CAPTCHA_ERROR: RetryConfig(
                max_retries=3,
                base_delay=5.0,
                max_delay=30.0,
                backoff_factor=1.5
            ),
            ErrorType.TIMEOUT_ERROR: RetryConfig(
                max_retries=3,
                base_delay=2.0,
                max_delay=20.0,
                backoff_factor=2.0
            ),
            ErrorType.UNKNOWN_ERROR: RetryConfig(
                max_retries=2,
                base_delay=5.0,
                max_delay=30.0,
                backoff_factor=2.0
            )
        }
        
    def classify_error(self, error_message: str, status_code: Optional[int] = None) -> ErrorType:
        """分类错误类型"""
        error_message_lower = error_message.lower()
        
        # 根据状态码分类
        if status_code:
            if status_code == 404:
                return ErrorType.PAGE_NOT_FOUND
            elif status_code == 429:
                return ErrorType.RATE_LIMITED
            elif status_code in [403, 451]:
                return ErrorType.IP_BLOCKED
            elif status_code >= 500:
                return ErrorType.NETWORK_ERROR
                
        # 根据错误消息分类
        for error_type, patterns in self.error_patterns.items():
            for pattern in patterns:
                if pattern in error_message_lower:
                    return error_type
                    
        return ErrorType.UNKNOWN_ERROR
        
    def calculate_delay(self, error_type: ErrorType, attempt: int) -> float:
        """计算重试延迟"""
        config = self.retry_configs.get(error_type, self.retry_configs[ErrorType.UNKNOWN_ERROR])
        
        # 指数退避
        delay = config.base_delay * (config.backoff_factor ** (attempt - 1))
        delay = min(delay, config.max_delay)
        
        # 添加随机抖动
        if config.jitter:
            jitter_range = delay * 0.1
            delay += random.uniform(-jitter_range, jitter_range)
            
        return max(delay, 0.1)
        
    def should_retry(self, error_type: ErrorType, attempt: int) -> bool:
        """判断是否应该重试"""
        config = self.retry_configs.get(error_type, self.retry_configs[ErrorType.UNKNOWN_ERROR])
        return attempt <= config.max_retries
        
    def record_error(self, error_type: ErrorType, error_message: str):
        """记录错误历史"""
        self.error_history.append({
            'timestamp': time.time(),
            'error_type': error_type,
            'error_message': error_message
        })
        
        # 保持最近100条记录
        if len(self.error_history) > 100:
            self.error_history = self.error_history[-100:]
            
    def get_error_statistics(self) -> Dict[str, Any]:
        """获取错误统计"""
        if not self.error_history:
            return {}
            
        # 统计各类错误数量
        error_counts = {}
        recent_errors = [e for e in self.error_history if time.time() - e['timestamp'] < 3600]  # 最近1小时
        
        for error in recent_errors:
            error_type = error['error_type'].value
            error_counts[error_type] = error_counts.get(error_type, 0) + 1
            
        return {
            'total_errors': len(recent_errors),
            'error_counts': error_counts,
            'most_common_error': max(error_counts.items(), key=lambda x: x[1])[0] if error_counts else None
        }
        
    def suggest_strategy(self) -> Dict[str, Any]:
        """建议优化策略"""
        stats = self.get_error_statistics()
        suggestions = []
        
        if not stats:
            return {'suggestions': ['暂无错误数据']}
            
        error_counts = stats.get('error_counts', {})
        
        # 根据错误类型提供建议
        if error_counts.get('ip_blocked', 0) > 3:
            suggestions.append("IP被封次数过多，建议使用代理或更换网络环境")
            
        if error_counts.get('captcha_error', 0) > 5:
            suggestions.append("验证码出现频繁，建议降低请求频率或优化反检测策略")
            
        if error_counts.get('rate_limited', 0) > 3:
            suggestions.append("频率限制触发，建议增加请求间隔")
            
        if error_counts.get('network_error', 0) > 5:
            suggestions.append("网络错误频繁，建议检查网络连接或增加超时时间")
            
        if not suggestions:
            suggestions.append("错误情况正常，继续当前策略")
            
        return {
            'suggestions': suggestions,
            'statistics': stats
        }

class RetryDecorator:
    """重试装饰器"""
    
    def __init__(self, smart_retry: SmartRetry):
        self.smart_retry = smart_retry
        
    def __call__(self, func: Callable) -> Callable:
        def wrapper(*args, **kwargs):
            attempt = 0
            last_error = None
            
            while True:
                attempt += 1
                
                try:
                    result = func(*args, **kwargs)
                    
                    # 如果成功，重置错误计数
                    if attempt > 1:
                        logger.info(f"✅ 重试成功，尝试次数: {attempt}")
                        
                    return result
                    
                except Exception as e:
                    last_error = e
                    error_message = str(e)
                    
                    # 分类错误
                    error_type = self.smart_retry.classify_error(error_message)
                    
                    # 记录错误
                    self.smart_retry.record_error(error_type, error_message)
                    
                    # 判断是否重试
                    if not self.smart_retry.should_retry(error_type, attempt):
                        logger.error(f"❌ 重试次数已达上限，最终失败: {error_message}")
                        raise e
                        
                    # 计算延迟
                    delay = self.smart_retry.calculate_delay(error_type, attempt)
                    
                    logger.warning(f"⚠️ 第{attempt}次尝试失败 ({error_type.value}): {error_message}")
                    logger.info(f"⏳ 等待 {delay:.1f} 秒后重试...")
                    
                    time.sleep(delay)
                    
            # 这里不会执行到，但为了类型检查
            raise last_error
            
        return wrapper

# 使用示例
def create_smart_retry_decorator():
    """创建智能重试装饰器"""
    smart_retry = SmartRetry()
    return RetryDecorator(smart_retry), smart_retry

# 全局实例
SMART_RETRY = SmartRetry()
retry_with_smart_strategy = RetryDecorator(SMART_RETRY)
