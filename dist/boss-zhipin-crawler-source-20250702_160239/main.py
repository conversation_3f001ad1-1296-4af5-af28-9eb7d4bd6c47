#!/usr/bin/env python3
"""
BOSS直聘职位URL爬虫主程序 - 增强版
使用Position_Crawler_1项目的完整增强技术栈
集成高级反检测、智能重试、验证码处理等功能
"""

import sys
import logging
from position_crawler_core import PositionCrawlerCore
from crawler_config import config_manager, get_config, validate_config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('crawler.log', encoding='utf-8')
    ]
)

def main():
    """主函数"""
    print("🚀 BOSS直聘职位URL爬虫 - Position_Crawler_1增强版")
    print("🔥 集成高级反检测、智能重试、验证码处理技术")
    print("=" * 70)

    try:
        # 加载和验证配置
        config = get_config()
        config_issues = validate_config()

        if config_issues:
            print("⚠️ 配置验证发现问题:")
            for issue in config_issues:
                print(f"   • {issue}")
            print()

        # 打印配置摘要
        config_manager.print_config_summary()

        # 创建Position_Crawler_1增强版爬虫
        crawler = PositionCrawlerCore()

        # 使用配置中的城市和关键词
        cities = config.target_cities[:3]  # 前3个城市
        keywords = config.target_keywords[:3]  # 前3个关键词

        print(f"\n🎯 开始爬取任务:")
        print(f"   城市: {', '.join(cities)}")
        print(f"   关键词: {', '.join(keywords)}")
        print(f"   每个搜索最大页数: {config.max_pages_per_search}")
        print()

        # 使用Position_Crawler_1增强技术爬取
        urls = crawler.crawl_boss_jobs(
            cities=cities,
            keywords=keywords
        )

        # 保存结果
        filepath = crawler.save_results()

        # 打印统计信息
        crawler.print_statistics()

        print(f"\n🎉 Position_Crawler_1增强版爬取完成！")
        print(f"📊 共获取 {len(urls)} 个唯一职位URL")
        print(f"💾 结果已保存到: {filepath}")

        # 如果没有获取到URL，提供建议
        if len(urls) == 0:
            print("\n💡 未获取到URL的可能原因和建议:")
            print("   • BOSS直聘反爬虫检测严格，建议:")
            print("     - 增加延迟时间 (delay_min, delay_max)")
            print("     - 使用代理IP")
            print("     - 降低并发数量")
            print("     - 手动处理验证码")
            print("   • 检查网络连接和目标网站可访问性")
            print("   • 查看 crawler.log 日志文件获取详细信息")

    except KeyboardInterrupt:
        print("\n⚠️ 用户中断程序")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 程序异常: {str(e)}")
        logging.exception("程序执行异常")
        print("📋 详细错误信息已记录到 crawler.log 文件")
        sys.exit(1)

if __name__ == "__main__":
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        sys.exit(1)

    # 运行主程序
    main()
