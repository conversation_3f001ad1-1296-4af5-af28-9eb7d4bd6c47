#!/bin/bash
# BOSS直聘爬虫项目安装脚本

echo "🚀 开始安装BOSS直聘爬虫项目..."

# 检查Python版本
python_version=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
required_version="3.8"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
    echo "❌ Python版本过低，需要3.8或更高版本"
    exit 1
fi

echo "✅ Python版本检查通过: $python_version"

# 安装依赖
echo "📦 安装项目依赖..."
pip3 install -r requirements.txt

# 运行设置脚本
echo "⚙️ 运行项目设置..."
python3 setup.py

# 运行健康检查
echo "🔍 运行健康检查..."
python3 health_check.py

echo "🎉 安装完成！"
echo "📋 快速开始:"
echo "   python3 quick_start.py"
echo "   python3 project_manager.py"
