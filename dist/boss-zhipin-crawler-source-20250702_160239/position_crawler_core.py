#!/usr/bin/env python3
"""
Position_Crawler_1项目核心逻辑实现 - 增强版
专门解决BOSS直聘反爬虫检测的核心技术
集成高级反检测、智能重试、验证码处理等功能
"""

import time
import random
import re
import logging
import json
from typing import List, Set, Dict, Any, Optional
from urllib.parse import urljoin
from bs4 import BeautifulSoup

# 导入增强模块
from advanced_stealth import AdvancedStealth
from smart_retry import SMART_RETRY, retry_with_smart_strategy

logger = logging.getLogger(__name__)

class PositionCrawlerCore:
    """Position_Crawler_1项目的核心爬虫逻辑 - 增强版"""

    def __init__(self):
        self.base_url = "https://www.zhipin.com"
        self.collected_urls = set()

        # 初始化增强模块
        self.stealth = AdvancedStealth()
        self.smart_retry = SMART_RETRY

        # Position_Crawler_1的核心配置
        self.config = {
            'delay_min': 8,  # 增加延迟
            'delay_max': 15,
            'max_pages': 10,
            'use_stealth_mode': True,
            'simulate_human': True,
            'handle_captcha': True,
            'max_captcha_retries': 3
        }

        # 统计信息
        self.stats = {
            'pages_crawled': 0,
            'urls_found': 0,
            'unique_urls': 0,
            'captcha_encountered': 0,
            'captcha_solved': 0,
            'method_used': 'Position_Crawler_Core_Enhanced',
            'errors': []
        }
        
    def get_stealth_headers(self) -> dict:
        """获取隐蔽性请求头"""
        return {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Accept-Encoding": "gzip, deflate, br",
            "DNT": "1",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1",
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "none",
            "Sec-Fetch-User": "?1",
            "Cache-Control": "max-age=0"
        }
        
    @retry_with_smart_strategy
    def use_seleniumbase_stealth(self, search_params: dict) -> List[str]:
        """使用SeleniumBase隐蔽模式 - Position_Crawler_1增强版核心方法"""
        try:
            from seleniumbase import SB

            logger.info("🛡️ 启动Position_Crawler_1增强版核心技术")

            # 构造搜索URL
            url = self._build_search_url(search_params)
            logger.info(f"🔍 目标URL: {url}")

            # 获取高级Chrome选项
            chrome_options = self.stealth.get_advanced_chrome_options()

            with SB(uc=True, test=True, headless=True,
                   disable_csp=True, disable_ws=True,
                   block_images=True, do_not_track=True,
                   chromium_arg=" ".join(chrome_options)) as sb:

                # 注入反检测脚本
                self._inject_stealth_scripts(sb)

                # 第一步：访问首页建立会话
                logger.info("📱 第一步：建立会话...")
                sb.open("https://www.zhipin.com")

                # 检测验证码
                if self._handle_captcha_if_present(sb):
                    self.stats['captcha_encountered'] += 1

                sb.sleep(random.uniform(5, 8))

                # 第二步：增强人类行为模拟
                logger.info("🤖 第二步：增强人类行为模拟...")
                self._enhanced_human_simulation(sb)

                # 第三步：访问搜索页面
                logger.info("🎯 第三步：访问搜索页面...")
                sb.open(url)

                # 再次检测验证码
                if self._handle_captcha_if_present(sb):
                    self.stats['captcha_encountered'] += 1

                sb.sleep(random.uniform(8, 12))

                # 第四步：智能等待页面加载
                logger.info("⏳ 第四步：智能等待页面加载...")
                self._smart_wait_for_content(sb)

                # 第五步：深度人类行为模拟
                logger.info("📜 第五步：深度人类行为模拟...")
                self._deep_human_simulation(sb)

                # 第六步：获取页面内容
                logger.info("📄 第六步：获取页面内容...")
                html_content = sb.get_page_source()

                # 检查页面是否被重定向到验证页面
                if self._is_verification_page(html_content):
                    logger.warning("🔍 检测到验证页面，尝试处理...")
                    if self._handle_verification_page(sb):
                        html_content = sb.get_page_source()
                    else:
                        raise Exception("验证页面处理失败")

                # 第七步：提取职位URL
                logger.info("🔗 第七步：提取职位URL...")
                urls = self._extract_urls_from_html(html_content)

                if urls:
                    logger.info(f"✅ Position_Crawler_1增强版成功提取 {len(urls)} 个URL")
                    self.stats['pages_crawled'] += 1
                    self.stats['urls_found'] += len(urls)
                    return urls
                else:
                    logger.warning("⚠️ 未提取到URL，保存页面用于分析")
                    self._save_debug_page(html_content)
                    raise Exception("未提取到任何URL")

        except ImportError:
            logger.error("❌ SeleniumBase未安装，无法使用Position_Crawler_1核心技术")
            raise Exception("SeleniumBase未安装")
        except Exception as e:
            error_msg = f"Position_Crawler_1技术执行失败: {e}"
            logger.error(f"❌ {error_msg}")
            self.stats['errors'].append(error_msg)
            raise e
            
    def _build_search_url(self, params: dict) -> str:
        """构造搜索URL"""
        base_url = "https://www.zhipin.com/web/geek/job"
        query_parts = []
        
        if params.get('keyword'):
            query_parts.append(f"query={params['keyword']}")
        if params.get('city_code'):
            query_parts.append(f"city={params['city_code']}")
        if params.get('page', 1) > 1:
            query_parts.append(f"page={params['page']}")
            
        if query_parts:
            return f"{base_url}?" + "&".join(query_parts)
        return base_url

    def _inject_stealth_scripts(self, sb) -> None:
        """注入反检测脚本"""
        try:
            scripts = self.stealth.get_stealth_js_scripts()
            for script in scripts:
                sb.execute_script(script)
            logger.debug("✅ 反检测脚本注入完成")
        except Exception as e:
            logger.debug(f"反检测脚本注入失败: {e}")

    def _handle_captcha_if_present(self, sb) -> bool:
        """检测并处理验证码"""
        try:
            # 检测验证码元素
            captcha_selectors = [
                ".yidun",
                ".geetest",
                "#captcha",
                ".captcha",
                "[class*='captcha']",
                ".verification",
                "[class*='verification']"
            ]

            for selector in captcha_selectors:
                try:
                    if sb.is_element_present(selector):
                        logger.warning(f"🔍 检测到验证码: {selector}")

                        # 尝试处理验证码
                        if self._handle_captcha_element(sb, selector):
                            self.stats['captcha_solved'] += 1
                            return True
                        else:
                            logger.warning("⚠️ 验证码处理失败")

                except Exception:
                    continue

            return False

        except Exception as e:
            logger.debug(f"验证码检测异常: {e}")
            return False

    def _handle_captcha_element(self, sb, selector: str) -> bool:
        """处理具体的验证码元素"""
        try:
            # 滑块验证码处理
            if "yidun" in selector or "geetest" in selector:
                return self._handle_slider_captcha(sb, selector)

            # 点击验证码处理
            elif "click" in selector.lower():
                return self._handle_click_captcha(sb, selector)

            # 通用验证码处理
            else:
                return self._handle_general_captcha(sb, selector)

        except Exception as e:
            logger.error(f"验证码处理异常: {e}")
            return False

    def _handle_slider_captcha(self, sb, selector: str) -> bool:
        """处理滑块验证码"""
        try:
            logger.info("🎯 处理滑块验证码...")

            # 查找滑块按钮
            slider_selectors = [
                f"{selector} .yidun_slider",
                f"{selector} .geetest_slider_button",
                f"{selector} [class*='slider']"
            ]

            for slider_sel in slider_selectors:
                try:
                    if sb.is_element_present(slider_sel):
                        # 模拟人类滑动
                        sb.drag_and_drop(slider_sel, f"{selector} .yidun_bg-img")
                        sb.sleep(random.uniform(2, 4))

                        # 检查是否成功
                        if not sb.is_element_present(selector):
                            logger.info("✅ 滑块验证码处理成功")
                            return True

                except Exception:
                    continue

            return False

        except Exception as e:
            logger.error(f"滑块验证码处理失败: {e}")
            return False

    def _handle_click_captcha(self, sb, selector: str) -> bool:
        """处理点击验证码"""
        try:
            logger.info("🎯 处理点击验证码...")

            # 简单点击处理
            sb.click(selector)
            sb.sleep(random.uniform(1, 3))

            return not sb.is_element_present(selector)

        except Exception as e:
            logger.error(f"点击验证码处理失败: {e}")
            return False

    def _handle_general_captcha(self, sb, selector: str) -> bool:
        """处理通用验证码"""
        try:
            logger.info("🔍 检测到验证码，等待处理...")

            # 截图保存
            timestamp = int(time.time())
            screenshot_path = f"captcha_{timestamp}.png"
            sb.save_screenshot(screenshot_path)
            logger.info(f"📸 验证码截图已保存: {screenshot_path}")

            # 等待一段时间，模拟人工处理
            sb.sleep(random.uniform(10, 20))

            return True

        except Exception as e:
            logger.error(f"通用验证码处理失败: {e}")
            return False

    def _enhanced_human_simulation(self, sb) -> None:
        """增强的人类行为模拟"""
        try:
            # 随机滚动
            for _ in range(random.randint(2, 4)):
                sb.scroll_to_bottom()
                sb.sleep(random.uniform(0.5, 1.5))
                sb.scroll_to_top()
                sb.sleep(random.uniform(0.5, 1.5))

            # 随机点击空白区域
            try:
                sb.click("body")
                sb.sleep(random.uniform(0.2, 0.8))
            except:
                pass

            # 模拟鼠标移动
            sb.execute_script("""
                var event = new MouseEvent('mousemove', {
                    clientX: Math.random() * window.innerWidth,
                    clientY: Math.random() * window.innerHeight
                });
                document.dispatchEvent(event);
            """)

        except Exception as e:
            logger.debug(f"人类行为模拟异常: {e}")

    def _smart_wait_for_content(self, sb) -> None:
        """智能等待页面内容加载"""
        try:
            # 等待常见元素
            wait_selectors = [
                "li",
                ".job-list",
                "[class*='job']",
                "[class*='position']",
                "a[href*='job_detail']"
            ]

            for selector in wait_selectors:
                try:
                    sb.wait_for_element_visible(selector, timeout=10)
                    logger.debug(f"✅ 检测到元素: {selector}")
                    break
                except:
                    continue

            # 等待JavaScript执行
            sb.sleep(random.uniform(3, 6))

        except Exception as e:
            logger.debug(f"智能等待异常: {e}")

    def _deep_human_simulation(self, sb) -> None:
        """深度人类行为模拟"""
        try:
            # 模拟阅读行为
            for i in range(random.randint(3, 6)):
                # 随机滚动到不同位置
                scroll_position = random.uniform(0.1, 0.9)
                sb.execute_script(f"window.scrollTo(0, document.body.scrollHeight * {scroll_position});")

                # 停留时间模拟阅读
                sb.sleep(random.uniform(1, 3))

                # 偶尔点击链接（但不跳转）
                if random.random() < 0.3:
                    try:
                        links = sb.find_elements("a")
                        if links:
                            link = random.choice(links[:10])  # 只考虑前10个链接
                            sb.execute_script("arguments[0].focus();", link)
                            sb.sleep(random.uniform(0.2, 0.8))
                    except:
                        pass

        except Exception as e:
            logger.debug(f"深度人类行为模拟异常: {e}")

    def _is_verification_page(self, html_content: str) -> bool:
        """检查是否为验证页面"""
        verification_indicators = [
            "网站访客身份验证",
            "访客验证",
            "人机验证",
            "安全验证",
            "verification",
            "captcha",
            "请完成验证"
        ]

        content_lower = html_content.lower()
        for indicator in verification_indicators:
            if indicator.lower() in content_lower:
                return True

        return False

    def _handle_verification_page(self, sb) -> bool:
        """处理验证页面"""
        try:
            logger.warning("🔍 处理验证页面...")

            # 尝试处理页面上的验证码
            if self._handle_captcha_if_present(sb):
                sb.sleep(random.uniform(3, 6))
                return True

            # 如果没有验证码，等待一段时间
            logger.info("⏳ 等待验证页面自动跳转...")
            sb.sleep(random.uniform(10, 20))

            return True

        except Exception as e:
            logger.error(f"验证页面处理失败: {e}")
            return False
        
    def _extract_urls_from_html(self, html_content: str) -> List[str]:
        """从HTML中提取职位URL - Position_Crawler_1优化版"""
        soup = BeautifulSoup(html_content, 'html.parser')
        urls = []
        
        logger.info("🔍 使用Position_Crawler_1的URL提取算法...")
        
        # Position_Crawler_1的多层提取策略
        
        # 策略1: 查找所有包含job_detail的链接
        all_links = soup.find_all('a', href=True)
        for link in all_links:
            href = link.get('href')
            if href and 'job_detail' in href:
                full_url = urljoin(self.base_url, href)
                if self._is_valid_job_url(full_url):
                    urls.append(full_url)
        
        # 策略2: 正则表达式提取
        patterns = [
            r'https://www\.zhipin\.com/job_detail/[a-zA-Z0-9_-]+\.html',
            r'/job_detail/[a-zA-Z0-9_-]+\.html',
            r'job_detail/[a-zA-Z0-9_-]+\.html'
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, html_content)
            for match in matches:
                if not match.startswith('http'):
                    match = urljoin(self.base_url, match)
                if self._is_valid_job_url(match):
                    urls.append(match)
        
        # 策略3: 从JavaScript中提取
        js_patterns = [
            r'"encryptJobId":\s*"([^"]+)"',
            r'"jobId":\s*"([^"]+)"',
            r'"lid":\s*"([^"]+)"'
        ]
        
        for pattern in js_patterns:
            matches = re.findall(pattern, html_content)
            for match in matches:
                url = f"https://www.zhipin.com/job_detail/{match}.html"
                if self._is_valid_job_url(url):
                    urls.append(url)
        
        # 去重
        unique_urls = list(set(urls))
        logger.info(f"📊 Position_Crawler_1算法提取结果: {len(unique_urls)} 个唯一URL")
        
        return unique_urls
        
    def _is_valid_job_url(self, url: str) -> bool:
        """验证职位URL有效性"""
        if not url or not isinstance(url, str):
            return False
            
        patterns = [
            r'https://www\.zhipin\.com/job_detail/[a-zA-Z0-9_-]+\.html',
            r'https://www\.zhipin\.com/position_detail/[a-zA-Z0-9_-]+\.html'
        ]
        
        for pattern in patterns:
            if re.match(pattern, url):
                return True
        return False
        
    def _save_debug_page(self, html_content: str):
        """保存调试页面"""
        timestamp = int(time.time())
        filename = f"position_crawler_debug_{timestamp}.html"
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        logger.info(f"🔍 调试页面已保存: {filename}")
        
        # 简单分析
        soup = BeautifulSoup(html_content, 'html.parser')
        logger.info(f"📊 页面分析: 长度={len(html_content)}, 链接数={len(soup.find_all('a'))}")
        
    def crawl_boss_jobs(self, cities: List[str] = None, keywords: List[str] = None) -> Set[str]:
        """使用Position_Crawler_1技术爬取BOSS直聘职位"""
        logger.info("🚀 启动Position_Crawler_1核心爬虫技术")
        
        if not cities:
            cities = ["北京", "上海", "深圳"]
        if not keywords:
            keywords = ["Python", "Java", "前端"]
            
        # 城市代码映射
        city_codes = {
            "北京": "101010100",
            "上海": "101020100", 
            "深圳": "101280600",
            "杭州": "101210100",
            "广州": "101280100"
        }
        
        all_urls = set()
        
        # 核心爬取逻辑
        for city in cities:
            city_code = city_codes.get(city, "")
            for keyword in keywords:
                logger.info(f"🎯 爬取: {city} + {keyword}")
                
                search_params = {
                    'city_code': city_code,
                    'keyword': keyword,
                    'page': 1
                }
                
                # 使用Position_Crawler_1核心技术
                urls = self.use_seleniumbase_stealth(search_params)
                if urls:  # 确保urls不为None
                    all_urls.update(urls)
                
                logger.info(f"📈 当前累计URL数: {len(all_urls)}")
                
                # Position_Crawler_1的关键延迟策略
                delay = random.uniform(self.config['delay_min'], self.config['delay_max'])
                logger.info(f"⏱️ Position_Crawler_1延迟策略: {delay:.1f}秒")
                time.sleep(delay)
        
        self.stats['unique_urls'] = len(all_urls)
        self.collected_urls = all_urls
        
        logger.info(f"🎉 Position_Crawler_1技术完成，共获取 {len(all_urls)} 个职位URL")
        
        return all_urls
        
    def save_results(self, filename: str = None) -> str:
        """保存结果"""
        if not filename:
            timestamp = time.strftime('%Y%m%d_%H%M%S')
            filename = f"position_crawler_urls_{timestamp}.txt"
            
        os.makedirs("output", exist_ok=True)
        filepath = os.path.join("output", filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(f"# Position_Crawler_1技术爬取结果\n")
            f.write(f"# 爬取时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"# 使用技术: {self.stats['method_used']}\n")
            f.write(f"# 总计URL数: {len(self.collected_urls)}\n")
            f.write(f"# ==========================================\n\n")
            
            for url in sorted(self.collected_urls):
                f.write(url + '\n')
                
        logger.info(f"💾 结果已保存到: {filepath}")
        return filepath
        
    def print_statistics(self):
        """打印统计信息"""
        print("\n" + "=" * 80)
        print("📊 Position_Crawler_1增强版技术统计报告")
        print("=" * 80)
        print(f"🏆 使用技术: {self.stats['method_used']}")
        print(f"📄 爬取页面: {self.stats['pages_crawled']}")
        print(f"🔗 发现URL总数: {self.stats['urls_found']}")
        print(f"✨ 去重后URL数: {self.stats['unique_urls']}")
        print(f"🔍 遇到验证码: {self.stats['captcha_encountered']} 次")
        print(f"✅ 验证码解决: {self.stats['captcha_solved']} 次")

        if self.stats['errors']:
            print(f"❌ 错误数量: {len(self.stats['errors'])}")

        # 显示智能重试统计
        retry_stats = self.smart_retry.get_error_statistics()
        if retry_stats:
            print(f"🔄 重试统计: {retry_stats}")

        # 显示优化建议
        suggestions = self.smart_retry.suggest_strategy()
        if suggestions.get('suggestions'):
            print("💡 优化建议:")
            for suggestion in suggestions['suggestions']:
                print(f"   • {suggestion}")

        print("=" * 80)
