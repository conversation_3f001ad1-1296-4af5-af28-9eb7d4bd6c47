#!/usr/bin/env python3
"""
项目文档生成器
自动生成API文档、使用指南等
"""

import os
import ast
import time
import inspect
import importlib
from pathlib import Path
from typing import Dict, List, Any, Optional

class DocGenerator:
    """文档生成器"""
    
    def __init__(self):
        self.project_root = Path.cwd()
        self.docs_dir = self.project_root / "docs"
        self.docs_dir.mkdir(exist_ok=True)
        
    def extract_module_info(self, module_path: str) -> Dict[str, Any]:
        """提取模块信息"""
        try:
            # 读取源码
            with open(module_path, 'r', encoding='utf-8') as f:
                source = f.read()
                
            # 解析AST
            tree = ast.parse(source)
            
            module_info = {
                "name": Path(module_path).stem,
                "docstring": ast.get_docstring(tree) or "",
                "classes": [],
                "functions": [],
                "imports": []
            }
            
            for node in ast.walk(tree):
                if isinstance(node, ast.ClassDef):
                    class_info = {
                        "name": node.name,
                        "docstring": ast.get_docstring(node) or "",
                        "methods": []
                    }
                    
                    for item in node.body:
                        if isinstance(item, ast.FunctionDef):
                            method_info = {
                                "name": item.name,
                                "docstring": ast.get_docstring(item) or "",
                                "args": [arg.arg for arg in item.args.args]
                            }
                            class_info["methods"].append(method_info)
                            
                    module_info["classes"].append(class_info)
                    
                elif isinstance(node, ast.FunctionDef) and not any(
                    isinstance(parent, ast.ClassDef) for parent in ast.walk(tree)
                    if hasattr(parent, 'body') and node in getattr(parent, 'body', [])
                ):
                    func_info = {
                        "name": node.name,
                        "docstring": ast.get_docstring(node) or "",
                        "args": [arg.arg for arg in node.args.args]
                    }
                    module_info["functions"].append(func_info)
                    
                elif isinstance(node, (ast.Import, ast.ImportFrom)):
                    if isinstance(node, ast.Import):
                        for alias in node.names:
                            module_info["imports"].append(alias.name)
                    else:
                        module_info["imports"].append(node.module or "")
                        
            return module_info
            
        except Exception as e:
            print(f"⚠️ 提取模块信息失败 {module_path}: {e}")
            return {"name": Path(module_path).stem, "error": str(e)}
            
    def generate_api_docs(self) -> str:
        """生成API文档"""
        print("📚 生成API文档...")
        
        # 主要模块
        modules = [
            "position_crawler_core.py",
            "advanced_stealth.py", 
            "smart_retry.py",
            "data_analyzer.py",
            "url_validator.py",
            "performance_monitor.py",
            "proxy_manager.py",
            "crawler_scheduler.py",
            "crawler_config.py"
        ]
        
        doc_content = []
        doc_content.append("# 📚 API文档")
        doc_content.append("")
        doc_content.append(f"生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        doc_content.append("")
        doc_content.append("## 📋 模块概览")
        doc_content.append("")
        
        module_infos = []
        
        for module_file in modules:
            if (self.project_root / module_file).exists():
                info = self.extract_module_info(module_file)
                module_infos.append(info)
                
                # 添加到概览
                doc_content.append(f"- **{info['name']}** - {info.get('docstring', '').split('.')[0]}")
                
        doc_content.append("")
        
        # 详细文档
        for info in module_infos:
            if "error" in info:
                continue
                
            doc_content.append(f"## 📦 {info['name']}")
            doc_content.append("")
            
            if info.get('docstring'):
                doc_content.append(info['docstring'])
                doc_content.append("")
                
            # 类文档
            if info.get('classes'):
                doc_content.append("### 📋 类")
                doc_content.append("")
                
                for cls in info['classes']:
                    doc_content.append(f"#### `{cls['name']}`")
                    doc_content.append("")
                    
                    if cls.get('docstring'):
                        doc_content.append(cls['docstring'])
                        doc_content.append("")
                        
                    if cls.get('methods'):
                        doc_content.append("**方法:**")
                        doc_content.append("")
                        
                        for method in cls['methods']:
                            args_str = ", ".join(method.get('args', []))
                            doc_content.append(f"- `{method['name']}({args_str})`")
                            if method.get('docstring'):
                                doc_content.append(f"  - {method['docstring'].split('.')[0]}")
                                
                        doc_content.append("")
                        
            # 函数文档
            if info.get('functions'):
                doc_content.append("### 🔧 函数")
                doc_content.append("")
                
                for func in info['functions']:
                    args_str = ", ".join(func.get('args', []))
                    doc_content.append(f"#### `{func['name']}({args_str})`")
                    doc_content.append("")
                    
                    if func.get('docstring'):
                        doc_content.append(func['docstring'])
                        doc_content.append("")
                        
            doc_content.append("---")
            doc_content.append("")
            
        # 保存文档
        api_doc_path = self.docs_dir / "API_REFERENCE.md"
        with open(api_doc_path, 'w', encoding='utf-8') as f:
            f.write("\n".join(doc_content))
            
        print(f"✅ API文档已生成: {api_doc_path}")
        return str(api_doc_path)
        
    def generate_usage_guide(self) -> str:
        """生成使用指南"""
        print("📖 生成使用指南...")
        
        guide_content = f"""# 🚀 使用指南

生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}

## 🎯 快速开始

### 1. 环境准备
```bash
# 检查Python版本 (需要3.8+)
python --version

# 安装项目
python setup.py

# 运行健康检查
python health_check.py
```

### 2. 启动方式

#### 方式一：快速启动（推荐新手）
```bash
python quick_start.py
```

#### 方式二：项目管理器（推荐）
```bash
python project_manager.py
```

#### 方式三：直接运行
```bash
python main.py
```

## 🔧 配置说明

### 基础配置
编辑 `crawler_config.json`:
```json
{{
  "delay_min": 10.0,
  "delay_max": 20.0,
  "max_retries": 5,
  "target_cities": ["北京", "上海", "深圳"],
  "target_keywords": ["Python", "Java", "前端"]
}}
```

### 环境变量
编辑 `.env` 文件:
```
CRAWLER_ENV=development
CRAWLER_DEBUG=true
CRAWLER_LOG_LEVEL=INFO
```

## 📊 功能模块

### 🕷️ 核心爬虫
- **position_crawler_core.py** - Position_Crawler_1核心技术
- 支持SeleniumBase隐蔽模式
- 智能验证码处理
- 深度人类行为模拟

### 🛡️ 反检测技术
- **advanced_stealth.py** - 高级反检测
- 浏览器指纹伪造
- JavaScript反检测脚本
- 多种验证码处理

### 🔄 智能重试
- **smart_retry.py** - 智能重试系统
- 错误类型自动分类
- 自适应延迟策略
- 优化建议提供

### 📊 数据分析
- **data_analyzer.py** - 数据分析工具
- URL模式分析
- 数据质量检查
- 统计报告生成

### 🔍 URL验证
- **url_validator.py** - URL验证工具
- 异步批量验证
- 状态码分析
- 有效URL筛选

### 📈 性能监控
- **performance_monitor.py** - 性能监控
- 实时资源监控
- 趋势分析
- 性能优化建议

### 🌐 代理管理
- **proxy_manager.py** - 代理管理
- 代理池管理
- 健康检查
- 智能轮换

### 📅 智能调度
- **crawler_scheduler.py** - 任务调度
- 批量任务管理
- 并发控制
- 资源优化

## 🎮 项目管理器

项目管理器提供统一的操作界面：

1. **🕷️ 运行爬虫** - 执行爬取任务
2. **📅 智能调度器** - 批量任务调度
3. **📊 分析数据** - 数据分析工具
4. **🔍 验证URL** - URL有效性验证
5. **🌐 代理管理** - 代理池管理
6. **📈 性能监控** - 系统性能监控
7. **⚙️ 配置管理** - 参数配置
8. **📁 文件管理** - 文件操作
9. **📋 项目状态** - 状态查看
10. **🧹 清理项目** - 文件清理

## ⚠️ 注意事项

### BOSS直聘反爬虫
- BOSS直聘具有严格的反爬虫机制
- 可能出现IP异常检测
- 会重定向到验证码页面
- 建议使用Position_Crawler_1项目的完整解决方案

### 使用建议
- 合理设置延迟时间（15-30秒）
- 避免高峰时段运行
- 遵守网站使用条款
- 定期清理临时文件

### 故障排除
1. **检查日志文件** - 查看详细错误信息
2. **运行健康检查** - `python health_check.py`
3. **重置配置** - 删除配置文件重新生成
4. **重新安装** - `python setup.py`

## 📞 技术支持

- **README.md** - 项目概述
- **SOLUTION_GUIDE.md** - 解决方案指南
- **API_REFERENCE.md** - API文档
- **FINAL_COMPLETION_REPORT.md** - 完整功能报告

## 🎯 最佳实践

### 生产环境
- 使用Position_Crawler_1项目（推荐）
- 配置代理IP池
- 增加延迟时间
- 监控系统资源

### 学习研究
- 研究各模块实现
- 分析反爬虫技术
- 学习架构设计
- 参考代码实现

### 扩展开发
- 基于现有架构扩展
- 适配其他目标网站
- 集成新的反检测技术
- 优化性能和稳定性
"""

        # 保存指南
        guide_path = self.docs_dir / "USAGE_GUIDE.md"
        with open(guide_path, 'w', encoding='utf-8') as f:
            f.write(guide_content)
            
        print(f"✅ 使用指南已生成: {guide_path}")
        return str(guide_path)
        
    def generate_changelog(self) -> str:
        """生成更新日志"""
        print("📝 生成更新日志...")
        
        changelog_content = f"""# 📝 更新日志

## [1.0.0] - {time.strftime('%Y-%m-%d')}

### 🎉 首次发布
- 完整的BOSS直聘爬虫系统
- 基于Position_Crawler_1核心技术

### ✨ 新功能
- 🕷️ Position_Crawler_1增强版核心爬虫
- 🛡️ 高级反检测技术（浏览器指纹伪造）
- 🔄 智能重试系统（错误分类、自适应延迟）
- 📊 数据分析工具（URL模式分析、质量检查）
- 🔍 URL验证工具（异步批量验证）
- 📈 性能监控模块（实时监控、趋势分析）
- 🌐 代理管理系统（代理池、健康检查）
- 📅 智能调度器（任务调度、并发控制）
- ⚙️ 配置管理系统（动态配置、环境适配）
- 🎮 项目管理器（统一界面、功能集成）

### 🔧 技术特性
- 模块化架构设计
- 异步并发处理
- 智能错误处理
- 完善的日志系统
- 用户友好界面

### 📚 文档
- 完整的使用指南
- API参考文档
- 解决方案指南
- 项目完成报告

### 🛠️ 工具
- 自动化安装脚本
- 项目健康检查
- 快速启动工具
- 文档生成器

### ⚠️ 已知问题
- BOSS直聘反爬虫机制严格，可能需要Position_Crawler_1完整解决方案
- 某些功能需要特定依赖库

### 🎯 未来计划
- 集成更多反检测技术
- 支持更多目标网站
- 添加数据可视化
- 优化性能和稳定性

---

## 版本说明

### 版本号格式
采用语义化版本号：`主版本.次版本.修订版本`

### 更新类型
- 🎉 **新功能** - 添加新的功能特性
- 🔧 **改进** - 现有功能的改进和优化
- 🐛 **修复** - Bug修复
- 📚 **文档** - 文档更新
- ⚠️ **破坏性变更** - 不兼容的API变更

### 发布周期
- **主版本** - 重大功能更新或架构变更
- **次版本** - 新功能添加或重要改进
- **修订版本** - Bug修复和小幅改进
"""

        # 保存更新日志
        changelog_path = self.docs_dir / "CHANGELOG.md"
        with open(changelog_path, 'w', encoding='utf-8') as f:
            f.write(changelog_content)
            
        print(f"✅ 更新日志已生成: {changelog_path}")
        return str(changelog_path)
        
    def generate_all_docs(self) -> List[str]:
        """生成所有文档"""
        print("📚 开始生成项目文档...")
        
        generated_docs = []
        
        try:
            # 生成API文档
            api_doc = self.generate_api_docs()
            generated_docs.append(api_doc)
            
            # 生成使用指南
            usage_guide = self.generate_usage_guide()
            generated_docs.append(usage_guide)
            
            # 生成更新日志
            changelog = self.generate_changelog()
            generated_docs.append(changelog)
            
            print(f"✅ 文档生成完成，共生成 {len(generated_docs)} 个文档")
            
        except Exception as e:
            print(f"❌ 文档生成失败: {e}")
            
        return generated_docs

def main():
    """主函数"""
    generator = DocGenerator()
    docs = generator.generate_all_docs()
    
    if docs:
        print("\n📚 生成的文档:")
        for doc in docs:
            print(f"   📄 {doc}")
    else:
        print("❌ 没有生成任何文档")

if __name__ == "__main__":
    main()
