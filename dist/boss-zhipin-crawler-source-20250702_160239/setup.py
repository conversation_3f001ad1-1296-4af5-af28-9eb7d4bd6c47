#!/usr/bin/env python3
"""
项目自动化安装和配置脚本
"""

import os
import sys
import subprocess
import platform
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ProjectSetup:
    """项目安装配置器"""
    
    def __init__(self):
        self.project_root = Path.cwd()
        self.python_version = sys.version_info
        self.system = platform.system()
        
    def check_requirements(self) -> bool:
        """检查系统要求"""
        logger.info("🔍 检查系统要求...")
        
        # 检查Python版本
        if self.python_version < (3, 8):
            logger.error("❌ 需要Python 3.8或更高版本")
            return False
        else:
            logger.info(f"✅ Python版本: {self.python_version.major}.{self.python_version.minor}")
            
        # 检查pip
        try:
            subprocess.run([sys.executable, "-m", "pip", "--version"], 
                         check=True, capture_output=True)
            logger.info("✅ pip已安装")
        except subprocess.CalledProcessError:
            logger.error("❌ pip未安装或不可用")
            return False
            
        return True
        
    def install_dependencies(self) -> bool:
        """安装依赖"""
        logger.info("📦 安装项目依赖...")
        
        try:
            # 升级pip
            subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                         check=True)
            logger.info("✅ pip已升级")
            
            # 安装requirements.txt
            if (self.project_root / "requirements.txt").exists():
                subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                             check=True)
                logger.info("✅ 依赖安装完成")
            else:
                logger.warning("⚠️ requirements.txt文件不存在")
                
            # 安装Playwright浏览器
            try:
                subprocess.run([sys.executable, "-m", "playwright", "install", "chromium"], 
                             check=True, capture_output=True)
                logger.info("✅ Playwright浏览器安装完成")
            except subprocess.CalledProcessError:
                logger.warning("⚠️ Playwright浏览器安装失败，某些功能可能不可用")
                
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ 依赖安装失败: {e}")
            return False
            
    def create_directories(self) -> None:
        """创建必要的目录"""
        logger.info("📁 创建项目目录...")
        
        directories = [
            "output",
            "logs", 
            "config",
            "data",
            "temp"
        ]
        
        for dir_name in directories:
            dir_path = self.project_root / dir_name
            dir_path.mkdir(exist_ok=True)
            logger.info(f"✅ 创建目录: {dir_name}")
            
    def create_config_files(self) -> None:
        """创建配置文件"""
        logger.info("⚙️ 创建配置文件...")
        
        # 创建默认配置
        config_content = {
            "base_url": "https://www.zhipin.com",
            "delay_min": 10.0,
            "delay_max": 20.0,
            "max_retries": 5,
            "target_cities": ["北京", "上海", "深圳"],
            "target_keywords": ["Python", "Java", "前端"],
            "output_dir": "output",
            "headless": True,
            "use_stealth_mode": True
        }
        
        config_file = self.project_root / "crawler_config.json"
        if not config_file.exists():
            import json
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config_content, f, ensure_ascii=False, indent=2)
            logger.info("✅ 创建默认配置文件")
            
        # 创建环境变量文件
        env_file = self.project_root / ".env"
        if not env_file.exists():
            env_content = """# 爬虫环境配置
CRAWLER_ENV=development
CRAWLER_DEBUG=true
CRAWLER_LOG_LEVEL=INFO
# CRAWLER_PROXY_URL=http://proxy:port
# CRAWLER_USER_AGENT=custom_user_agent
"""
            with open(env_file, 'w', encoding='utf-8') as f:
                f.write(env_content)
            logger.info("✅ 创建环境变量文件")
            
    def create_scripts(self) -> None:
        """创建启动脚本"""
        logger.info("📜 创建启动脚本...")
        
        # Windows批处理脚本
        if self.system == "Windows":
            bat_content = """@echo off
echo 启动BOSS直聘爬虫项目管理器...
python project_manager.py
pause
"""
            with open("start_crawler.bat", 'w', encoding='utf-8') as f:
                f.write(bat_content)
            logger.info("✅ 创建Windows启动脚本")
            
        # Unix shell脚本
        else:
            sh_content = """#!/bin/bash
echo "启动BOSS直聘爬虫项目管理器..."
python3 project_manager.py
"""
            with open("start_crawler.sh", 'w', encoding='utf-8') as f:
                f.write(sh_content)
            os.chmod("start_crawler.sh", 0o755)
            logger.info("✅ 创建Unix启动脚本")
            
    def run_tests(self) -> bool:
        """运行基础测试"""
        logger.info("🧪 运行基础测试...")
        
        try:
            # 测试导入主要模块
            test_modules = [
                "position_crawler_core",
                "advanced_stealth", 
                "smart_retry",
                "data_analyzer",
                "url_validator",
                "performance_monitor",
                "proxy_manager",
                "crawler_scheduler",
                "project_manager"
            ]
            
            for module in test_modules:
                try:
                    __import__(module)
                    logger.info(f"✅ 模块测试通过: {module}")
                except ImportError as e:
                    logger.warning(f"⚠️ 模块导入失败: {module} - {e}")
                    
            # 测试配置加载
            try:
                from crawler_config import get_config
                config = get_config()
                logger.info("✅ 配置加载测试通过")
            except Exception as e:
                logger.warning(f"⚠️ 配置加载测试失败: {e}")
                
            return True
            
        except Exception as e:
            logger.error(f"❌ 测试失败: {e}")
            return False
            
    def setup(self) -> bool:
        """执行完整安装"""
        logger.info("🚀 开始项目安装配置...")
        
        # 检查系统要求
        if not self.check_requirements():
            return False
            
        # 安装依赖
        if not self.install_dependencies():
            return False
            
        # 创建目录和文件
        self.create_directories()
        self.create_config_files()
        self.create_scripts()
        
        # 运行测试
        if not self.run_tests():
            logger.warning("⚠️ 部分测试失败，但安装继续")
            
        logger.info("🎉 项目安装配置完成！")
        return True
        
    def show_usage_info(self) -> None:
        """显示使用说明"""
        print("\n" + "=" * 80)
        print("🎉 BOSS直聘爬虫项目安装完成！")
        print("=" * 80)
        print("📋 快速开始:")
        print("1. 运行项目管理器:")
        print("   python project_manager.py")
        print("")
        print("2. 或使用启动脚本:")
        if self.system == "Windows":
            print("   双击 start_crawler.bat")
        else:
            print("   ./start_crawler.sh")
        print("")
        print("3. 直接运行爬虫:")
        print("   python main.py")
        print("")
        print("📖 文档:")
        print("- README.md - 项目使用指南")
        print("- SOLUTION_GUIDE.md - 解决方案指南")
        print("- FINAL_COMPLETION_REPORT.md - 完整功能报告")
        print("")
        print("⚙️ 配置文件:")
        print("- crawler_config.json - 爬虫配置")
        print("- .env - 环境变量")
        print("")
        print("🆘 如遇问题:")
        print("1. 查看日志文件: logs/")
        print("2. 检查配置文件")
        print("3. 参考文档说明")
        print("=" * 80)

def main():
    """主函数"""
    print("🚀 BOSS直聘爬虫项目自动安装程序")
    print("=" * 50)
    
    setup = ProjectSetup()
    
    # 询问用户确认
    confirm = input("是否开始安装配置？(y/n): ").lower()
    if confirm != 'y':
        print("安装已取消")
        return
        
    # 执行安装
    if setup.setup():
        setup.show_usage_info()
    else:
        print("❌ 安装失败，请检查错误信息")
        sys.exit(1)

if __name__ == "__main__":
    main()
