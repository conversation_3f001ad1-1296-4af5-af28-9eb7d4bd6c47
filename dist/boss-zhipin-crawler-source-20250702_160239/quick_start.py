#!/usr/bin/env python3
"""
快速启动脚本
为新用户提供简化的启动体验
"""

import os
import sys
import time
import logging
from pathlib import Path

# 配置简单日志
logging.basicConfig(level=logging.INFO, format='%(message)s')
logger = logging.getLogger(__name__)

def print_banner():
    """打印欢迎横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                                                                              ║
║                    🚀 BOSS直聘爬虫项目 - 快速启动                              ║
║                                                                              ║
║                   基于Position_Crawler_1核心技术                             ║
║                   集成高级反检测、智能重试、数据分析                            ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
"""
    print(banner)

def check_first_run():
    """检查是否首次运行"""
    marker_file = Path(".first_run_complete")
    return not marker_file.exists()

def run_setup():
    """运行初始设置"""
    print("🔧 检测到首次运行，开始初始化设置...")
    
    try:
        from setup import ProjectSetup
        setup = ProjectSetup()
        
        print("📦 正在安装依赖和配置项目...")
        if setup.setup():
            # 创建首次运行标记
            Path(".first_run_complete").touch()
            print("✅ 初始化完成！")
            return True
        else:
            print("❌ 初始化失败")
            return False
            
    except ImportError:
        print("⚠️ 安装脚本不可用，跳过自动设置")
        return True
    except Exception as e:
        print(f"❌ 设置过程出错: {e}")
        return False

def show_quick_menu():
    """显示快速菜单"""
    print("\n" + "=" * 60)
    print("🎯 快速启动菜单")
    print("=" * 60)
    print("1. 🚀 启动项目管理器 (推荐)")
    print("2. 🕷️ 直接运行爬虫")
    print("3. 🔍 运行健康检查")
    print("4. 📊 数据分析工具")
    print("5. 📖 查看帮助文档")
    print("6. ⚙️ 高级设置")
    print("7. 🚪 退出")
    print("=" * 60)

def run_project_manager():
    """运行项目管理器"""
    print("🚀 启动项目管理器...")
    try:
        from project_manager import ProjectManager
        manager = ProjectManager()
        manager.run()
    except ImportError:
        print("❌ 项目管理器模块未找到")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

def run_crawler():
    """直接运行爬虫"""
    print("🕷️ 启动爬虫...")
    
    # 简单配置
    print("请选择爬取配置:")
    print("1. 快速测试 (北京+上海, Python+Java, 2页)")
    print("2. 标准爬取 (3城市, 3关键词, 3页)")
    print("3. 自定义配置")
    
    choice = input("请选择 (默认1): ").strip() or "1"
    
    try:
        from position_crawler_core import PositionCrawlerCore
        
        crawler = PositionCrawlerCore()
        
        if choice == "1":
            cities = ["北京", "上海"]
            keywords = ["Python", "Java"]
            print(f"🎯 快速测试: {cities} + {keywords}")
        elif choice == "2":
            cities = ["北京", "上海", "深圳"]
            keywords = ["Python", "Java", "前端"]
            print(f"🎯 标准爬取: {cities} + {keywords}")
        elif choice == "3":
            cities_input = input("请输入城市(用逗号分隔): ").strip()
            keywords_input = input("请输入关键词(用逗号分隔): ").strip()
            cities = [c.strip() for c in cities_input.split(',') if c.strip()]
            keywords = [k.strip() for k in keywords_input.split(',') if k.strip()]
            print(f"🎯 自定义配置: {cities} + {keywords}")
        else:
            print("❌ 无效选择")
            return
            
        # 执行爬取
        urls = crawler.crawl_boss_jobs(cities=cities, keywords=keywords)
        
        if urls:
            filepath = crawler.save_results()
            crawler.print_statistics()
            print(f"\n🎉 爬取完成！共获取 {len(urls)} 个URL")
            print(f"💾 结果已保存到: {filepath}")
        else:
            print("❌ 未获取到任何URL")
            
    except ImportError:
        print("❌ 爬虫模块未找到")
    except Exception as e:
        print(f"❌ 爬虫运行失败: {e}")

def run_health_check():
    """运行健康检查"""
    print("🔍 运行项目健康检查...")
    try:
        from health_check import HealthChecker
        checker = HealthChecker()
        results = checker.run_all_checks()
        report = checker.generate_report(results)
        print(report)
    except ImportError:
        print("❌ 健康检查模块未找到")
    except Exception as e:
        print(f"❌ 健康检查失败: {e}")

def run_data_analysis():
    """运行数据分析"""
    print("📊 启动数据分析工具...")
    
    # 查找数据文件
    import glob
    files = glob.glob("output/*.txt") + glob.glob("*.txt")
    url_files = [f for f in files if 'url' in f.lower()]
    
    if not url_files:
        print("❌ 没有找到数据文件")
        print("💡 请先运行爬虫获取数据")
        return
        
    print("可用的数据文件:")
    for i, file in enumerate(url_files, 1):
        print(f"{i}. {os.path.basename(file)}")
        
    try:
        choice = int(input("请选择文件编号: ")) - 1
        if 0 <= choice < len(url_files):
            filepath = url_files[choice]
            
            from data_analyzer import analyze_crawled_data
            analyze_crawled_data(filepath)
        else:
            print("❌ 无效选择")
    except ValueError:
        print("❌ 请输入有效数字")
    except ImportError:
        print("❌ 数据分析模块未找到")
    except Exception as e:
        print(f"❌ 数据分析失败: {e}")

def show_help():
    """显示帮助信息"""
    help_text = """
📖 BOSS直聘爬虫项目帮助

🎯 项目功能:
- 基于Position_Crawler_1核心技术的专业爬虫
- 高级反检测技术（浏览器指纹伪造、行为模拟）
- 智能重试系统（错误分类、自适应延迟）
- 完整的数据分析工具
- 实时性能监控
- 代理管理系统

🚀 快速开始:
1. 首次使用建议选择"项目管理器"
2. 项目管理器提供完整的功能界面
3. 可以直接运行爬虫进行快速测试

📋 主要文件:
- project_manager.py - 项目管理器（推荐）
- main.py - 直接运行爬虫
- health_check.py - 健康检查工具
- data_analyzer.py - 数据分析工具

⚠️ 重要说明:
- BOSS直聘具有严格的反爬虫机制
- 建议合理设置延迟时间
- 遵守网站使用条款
- 如遇验证码，说明需要更高级的技术

📞 技术支持:
- 查看 README.md 获取详细说明
- 参考 SOLUTION_GUIDE.md 了解技术方案
- 检查日志文件排查问题
"""
    print(help_text)

def advanced_settings():
    """高级设置"""
    print("⚙️ 高级设置...")
    
    print("高级功能:")
    print("1. 重新运行初始化设置")
    print("2. 清理项目文件")
    print("3. 重置配置文件")
    print("4. 查看项目状态")
    print("5. 返回主菜单")
    
    choice = input("请选择: ").strip()
    
    if choice == "1":
        if os.path.exists(".first_run_complete"):
            os.remove(".first_run_complete")
        run_setup()
    elif choice == "2":
        confirm = input("确认清理项目文件？(y/n): ").lower()
        if confirm == 'y':
            # 清理临时文件
            import glob
            patterns = ['*.log', 'debug_*.html', 'captcha_*.png', '*.tmp']
            count = 0
            for pattern in patterns:
                for file in glob.glob(pattern):
                    try:
                        os.remove(file)
                        count += 1
                    except:
                        pass
            print(f"✅ 清理了 {count} 个文件")
    elif choice == "3":
        config_file = "crawler_config.json"
        if os.path.exists(config_file):
            os.remove(config_file)
            print("✅ 配置文件已重置")
        else:
            print("⚠️ 配置文件不存在")
    elif choice == "4":
        run_health_check()
    elif choice == "5":
        return
    else:
        print("❌ 无效选择")

def main():
    """主函数"""
    print_banner()
    
    # 检查首次运行
    if check_first_run():
        if not run_setup():
            print("❌ 初始化失败，某些功能可能不可用")
            
    # 主循环
    while True:
        show_quick_menu()
        choice = input("请选择操作: ").strip()
        
        if choice == "1":
            run_project_manager()
        elif choice == "2":
            run_crawler()
        elif choice == "3":
            run_health_check()
        elif choice == "4":
            run_data_analysis()
        elif choice == "5":
            show_help()
        elif choice == "6":
            advanced_settings()
        elif choice == "7":
            print("👋 感谢使用，再见！")
            break
        else:
            print("❌ 无效选择，请重新输入")
            
        input("\n按回车键继续...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 用户中断，程序退出")
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        sys.exit(1)
