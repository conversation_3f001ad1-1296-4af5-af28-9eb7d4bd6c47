#!/usr/bin/env python3
"""
数据分析模块
对爬取的URL数据进行分析和统计
"""

import re
import json
import logging
from typing import Dict, List, Set, Any
from collections import Counter, defaultdict
from urllib.parse import urlparse, parse_qs
import time

logger = logging.getLogger(__name__)

class URLAnalyzer:
    """URL数据分析器"""
    
    def __init__(self):
        self.urls = []
        self.analysis_results = {}
        
    def load_urls_from_file(self, filepath: str) -> int:
        """从文件加载URL"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                
            # 过滤掉注释行和空行
            self.urls = [
                line.strip() 
                for line in lines 
                if line.strip() and not line.startswith('#')
            ]
            
            logger.info(f"✅ 从 {filepath} 加载了 {len(self.urls)} 个URL")
            return len(self.urls)
            
        except Exception as e:
            logger.error(f"❌ 加载URL文件失败: {e}")
            return 0
            
    def analyze_urls(self) -> Dict[str, Any]:
        """分析URL数据"""
        if not self.urls:
            logger.warning("⚠️ 没有URL数据可分析")
            return {}
            
        logger.info("🔍 开始分析URL数据...")
        
        results = {
            'basic_stats': self._analyze_basic_stats(),
            'url_patterns': self._analyze_url_patterns(),
            'job_ids': self._extract_job_ids(),
            'domain_analysis': self._analyze_domains(),
            'quality_check': self._check_url_quality()
        }
        
        self.analysis_results = results
        logger.info("✅ URL数据分析完成")
        
        return results
        
    def _analyze_basic_stats(self) -> Dict[str, Any]:
        """基础统计分析"""
        return {
            'total_urls': len(self.urls),
            'unique_urls': len(set(self.urls)),
            'duplicate_count': len(self.urls) - len(set(self.urls)),
            'duplicate_rate': (len(self.urls) - len(set(self.urls))) / len(self.urls) * 100,
            'avg_url_length': sum(len(url) for url in self.urls) / len(self.urls),
            'url_length_range': {
                'min': min(len(url) for url in self.urls),
                'max': max(len(url) for url in self.urls)
            }
        }
        
    def _analyze_url_patterns(self) -> Dict[str, Any]:
        """URL模式分析"""
        patterns = {
            'job_detail': 0,
            'position_detail': 0,
            'with_params': 0,
            'https_count': 0,
            'zhipin_domain': 0
        }
        
        for url in self.urls:
            if 'job_detail' in url:
                patterns['job_detail'] += 1
            if 'position_detail' in url:
                patterns['position_detail'] += 1
            if '?' in url:
                patterns['with_params'] += 1
            if url.startswith('https://'):
                patterns['https_count'] += 1
            if 'zhipin.com' in url:
                patterns['zhipin_domain'] += 1
                
        return patterns
        
    def _extract_job_ids(self) -> Dict[str, Any]:
        """提取职位ID"""
        job_ids = []
        id_patterns = [
            r'/job_detail/([a-zA-Z0-9_-]+)\.html',
            r'/position_detail/([a-zA-Z0-9_-]+)\.html'
        ]
        
        for url in self.urls:
            for pattern in id_patterns:
                matches = re.findall(pattern, url)
                job_ids.extend(matches)
                
        # 分析ID特征
        if job_ids:
            id_lengths = [len(job_id) for job_id in job_ids]
            id_chars = ''.join(job_ids)
            
            return {
                'total_ids': len(job_ids),
                'unique_ids': len(set(job_ids)),
                'id_length_stats': {
                    'min': min(id_lengths),
                    'max': max(id_lengths),
                    'avg': sum(id_lengths) / len(id_lengths)
                },
                'character_analysis': {
                    'has_numbers': any(c.isdigit() for c in id_chars),
                    'has_letters': any(c.isalpha() for c in id_chars),
                    'has_underscore': '_' in id_chars,
                    'has_dash': '-' in id_chars
                },
                'sample_ids': job_ids[:10]  # 前10个ID样本
            }
        else:
            return {'total_ids': 0, 'message': '未找到职位ID'}
            
    def _analyze_domains(self) -> Dict[str, Any]:
        """域名分析"""
        domains = []
        for url in self.urls:
            try:
                parsed = urlparse(url)
                domains.append(parsed.netloc)
            except:
                continue
                
        domain_counts = Counter(domains)
        
        return {
            'unique_domains': len(domain_counts),
            'domain_distribution': dict(domain_counts.most_common()),
            'main_domain': domain_counts.most_common(1)[0] if domain_counts else None
        }
        
    def _check_url_quality(self) -> Dict[str, Any]:
        """URL质量检查"""
        quality_issues = {
            'invalid_urls': [],
            'suspicious_urls': [],
            'malformed_urls': [],
            'missing_https': []
        }
        
        valid_count = 0
        
        for url in self.urls:
            # 基础URL格式检查
            if not url.startswith(('http://', 'https://')):
                quality_issues['malformed_urls'].append(url)
                continue
                
            # HTTPS检查
            if not url.startswith('https://'):
                quality_issues['missing_https'].append(url)
                
            # BOSS直聘URL格式检查
            if 'zhipin.com' in url:
                if not any(pattern in url for pattern in ['job_detail', 'position_detail']):
                    quality_issues['suspicious_urls'].append(url)
                else:
                    valid_count += 1
            else:
                quality_issues['invalid_urls'].append(url)
                
        return {
            'valid_urls': valid_count,
            'quality_score': valid_count / len(self.urls) * 100 if self.urls else 0,
            'issues': {k: len(v) for k, v in quality_issues.items()},
            'issue_details': quality_issues
        }
        
    def generate_report(self, save_to_file: bool = True) -> str:
        """生成分析报告"""
        if not self.analysis_results:
            self.analyze_urls()
            
        report_lines = []
        report_lines.append("=" * 80)
        report_lines.append("📊 URL数据分析报告")
        report_lines.append("=" * 80)
        report_lines.append(f"生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append("")
        
        # 基础统计
        basic = self.analysis_results.get('basic_stats', {})
        report_lines.append("📈 基础统计")
        report_lines.append("-" * 40)
        report_lines.append(f"总URL数量: {basic.get('total_urls', 0)}")
        report_lines.append(f"唯一URL数量: {basic.get('unique_urls', 0)}")
        report_lines.append(f"重复URL数量: {basic.get('duplicate_count', 0)}")
        report_lines.append(f"重复率: {basic.get('duplicate_rate', 0):.2f}%")
        report_lines.append(f"平均URL长度: {basic.get('avg_url_length', 0):.1f}")
        report_lines.append("")
        
        # URL模式
        patterns = self.analysis_results.get('url_patterns', {})
        report_lines.append("🔍 URL模式分析")
        report_lines.append("-" * 40)
        report_lines.append(f"job_detail类型: {patterns.get('job_detail', 0)}")
        report_lines.append(f"position_detail类型: {patterns.get('position_detail', 0)}")
        report_lines.append(f"带参数URL: {patterns.get('with_params', 0)}")
        report_lines.append(f"HTTPS协议: {patterns.get('https_count', 0)}")
        report_lines.append(f"BOSS直聘域名: {patterns.get('zhipin_domain', 0)}")
        report_lines.append("")
        
        # 职位ID分析
        job_ids = self.analysis_results.get('job_ids', {})
        report_lines.append("🆔 职位ID分析")
        report_lines.append("-" * 40)
        report_lines.append(f"提取的ID数量: {job_ids.get('total_ids', 0)}")
        report_lines.append(f"唯一ID数量: {job_ids.get('unique_ids', 0)}")
        if job_ids.get('sample_ids'):
            report_lines.append(f"ID样本: {', '.join(job_ids['sample_ids'][:5])}")
        report_lines.append("")
        
        # 质量检查
        quality = self.analysis_results.get('quality_check', {})
        report_lines.append("✅ 质量检查")
        report_lines.append("-" * 40)
        report_lines.append(f"有效URL数量: {quality.get('valid_urls', 0)}")
        report_lines.append(f"质量评分: {quality.get('quality_score', 0):.2f}%")
        
        issues = quality.get('issues', {})
        if any(issues.values()):
            report_lines.append("发现的问题:")
            for issue_type, count in issues.items():
                if count > 0:
                    report_lines.append(f"  - {issue_type}: {count}")
        else:
            report_lines.append("未发现质量问题")
            
        report_lines.append("")
        report_lines.append("=" * 80)
        
        report_text = "\n".join(report_lines)
        
        if save_to_file:
            timestamp = time.strftime('%Y%m%d_%H%M%S')
            filename = f"url_analysis_report_{timestamp}.txt"
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(report_text)
                logger.info(f"📄 分析报告已保存到: {filename}")
            except Exception as e:
                logger.error(f"❌ 保存报告失败: {e}")
                
        return report_text
        
    def export_analysis_json(self, filename: str = None) -> str:
        """导出分析结果为JSON"""
        if not filename:
            timestamp = time.strftime('%Y%m%d_%H%M%S')
            filename = f"url_analysis_{timestamp}.json"
            
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.analysis_results, f, ensure_ascii=False, indent=2)
            logger.info(f"📊 分析数据已导出到: {filename}")
            return filename
        except Exception as e:
            logger.error(f"❌ 导出JSON失败: {e}")
            return ""
            
    def get_recommendations(self) -> List[str]:
        """获取优化建议"""
        recommendations = []
        
        if not self.analysis_results:
            return ["请先运行数据分析"]
            
        basic = self.analysis_results.get('basic_stats', {})
        quality = self.analysis_results.get('quality_check', {})
        
        # 基于分析结果提供建议
        if basic.get('duplicate_rate', 0) > 10:
            recommendations.append("检测到较高的重复率，建议优化去重逻辑")
            
        if quality.get('quality_score', 0) < 80:
            recommendations.append("URL质量评分较低，建议检查URL提取逻辑")
            
        issues = quality.get('issues', {})
        if issues.get('invalid_urls', 0) > 0:
            recommendations.append("发现无效URL，建议加强URL验证")
            
        if issues.get('missing_https', 0) > 0:
            recommendations.append("部分URL缺少HTTPS，建议统一使用安全协议")
            
        if not recommendations:
            recommendations.append("数据质量良好，无需特别优化")
            
        return recommendations

def analyze_crawled_data(filepath: str) -> None:
    """分析爬取的数据"""
    analyzer = URLAnalyzer()
    
    # 加载数据
    count = analyzer.load_urls_from_file(filepath)
    if count == 0:
        print("❌ 没有数据可分析")
        return
        
    # 执行分析
    results = analyzer.analyze_urls()
    
    # 生成报告
    report = analyzer.generate_report()
    print(report)
    
    # 导出JSON
    analyzer.export_analysis_json()
    
    # 显示建议
    recommendations = analyzer.get_recommendations()
    print("\n💡 优化建议:")
    for i, rec in enumerate(recommendations, 1):
        print(f"   {i}. {rec}")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        analyze_crawled_data(sys.argv[1])
    else:
        print("使用方法: python data_analyzer.py <url_file_path>")
        print("示例: python data_analyzer.py output/position_crawler_urls_20250702_152000.txt")
