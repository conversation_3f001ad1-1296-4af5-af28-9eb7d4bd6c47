#!/usr/bin/env python3
"""
爬虫配置管理模块
基于Position_Crawler_1项目的配置管理最佳实践
支持动态配置调整、环境适配等功能
"""

import os
import json
import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from pathlib import Path

logger = logging.getLogger(__name__)

@dataclass
class CrawlerConfig:
    """爬虫配置类"""
    
    # 基础配置
    base_url: str = "https://www.zhipin.com"
    max_pages_per_search: int = 5
    max_concurrent_requests: int = 3
    
    # 延迟配置
    delay_min: float = 8.0
    delay_max: float = 15.0
    page_load_timeout: int = 30
    element_wait_timeout: int = 15
    
    # 反检测配置
    use_stealth_mode: bool = True
    simulate_human_behavior: bool = True
    handle_captcha: bool = True
    max_captcha_retries: int = 3
    
    # 重试配置
    max_retries: int = 5
    retry_delay_base: float = 2.0
    retry_delay_max: float = 60.0
    
    # 浏览器配置
    headless: bool = True
    disable_images: bool = True
    disable_css: bool = False
    user_agent: str = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    
    # 输出配置
    output_dir: str = "output"
    save_debug_info: bool = True
    save_screenshots: bool = True
    
    # 城市和关键词配置
    target_cities: List[str] = None
    target_keywords: List[str] = None
    
    def __post_init__(self):
        """初始化后处理"""
        if self.target_cities is None:
            self.target_cities = ["北京", "上海", "深圳"]
        if self.target_keywords is None:
            self.target_keywords = ["Python", "Java", "前端"]

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = "crawler_config.json"):
        self.config_file = Path(config_file)
        self.config = self._load_config()
        
    def _load_config(self) -> CrawlerConfig:
        """加载配置"""
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                    
                # 创建配置对象
                config = CrawlerConfig(**config_data)
                logger.info(f"✅ 配置已从 {self.config_file} 加载")
                return config
                
            except Exception as e:
                logger.warning(f"⚠️ 配置文件加载失败: {e}，使用默认配置")
                
        # 使用默认配置
        config = CrawlerConfig()
        self._save_config(config)
        return config
        
    def _save_config(self, config: CrawlerConfig) -> None:
        """保存配置"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(asdict(config), f, ensure_ascii=False, indent=2)
            logger.info(f"✅ 配置已保存到 {self.config_file}")
        except Exception as e:
            logger.error(f"❌ 配置保存失败: {e}")
            
    def get_config(self) -> CrawlerConfig:
        """获取当前配置"""
        return self.config
        
    def update_config(self, **kwargs) -> None:
        """更新配置"""
        for key, value in kwargs.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
                logger.info(f"🔧 配置更新: {key} = {value}")
            else:
                logger.warning(f"⚠️ 未知配置项: {key}")
                
        self._save_config(self.config)
        
    def get_city_codes(self) -> Dict[str, str]:
        """获取城市代码映射"""
        return {
            "北京": "101010100",
            "上海": "101020100", 
            "深圳": "101280600",
            "杭州": "101210100",
            "广州": "101280100",
            "成都": "101270100",
            "武汉": "101200100",
            "西安": "101110100",
            "南京": "101190100",
            "苏州": "101190400",
            "天津": "101030100",
            "重庆": "101040100",
            "青岛": "101120200",
            "大连": "101070200",
            "厦门": "101230200",
            "宁波": "101210400",
            "佛山": "101280800",
            "无锡": "101190200",
            "合肥": "101220100",
            "郑州": "101180100"
        }
        
    def get_environment_config(self) -> Dict[str, Any]:
        """获取环境相关配置"""
        env_config = {
            "is_production": os.getenv("CRAWLER_ENV") == "production",
            "debug_mode": os.getenv("CRAWLER_DEBUG", "false").lower() == "true",
            "log_level": os.getenv("CRAWLER_LOG_LEVEL", "INFO"),
            "proxy_url": os.getenv("CRAWLER_PROXY_URL"),
            "user_agent": os.getenv("CRAWLER_USER_AGENT", self.config.user_agent)
        }
        
        # 根据环境调整配置
        if env_config["is_production"]:
            self.config.headless = True
            self.config.save_debug_info = False
            self.config.delay_min = max(self.config.delay_min, 10.0)
            
        if env_config["debug_mode"]:
            self.config.headless = False
            self.config.save_debug_info = True
            self.config.save_screenshots = True
            
        return env_config
        
    def validate_config(self) -> List[str]:
        """验证配置有效性"""
        issues = []
        
        # 检查延迟配置
        if self.config.delay_min >= self.config.delay_max:
            issues.append("delay_min 应该小于 delay_max")
            
        if self.config.delay_min < 1.0:
            issues.append("delay_min 不应小于 1.0 秒")
            
        # 检查重试配置
        if self.config.max_retries < 1:
            issues.append("max_retries 应该至少为 1")
            
        if self.config.retry_delay_base >= self.config.retry_delay_max:
            issues.append("retry_delay_base 应该小于 retry_delay_max")
            
        # 检查城市和关键词
        if not self.config.target_cities:
            issues.append("target_cities 不能为空")
            
        if not self.config.target_keywords:
            issues.append("target_keywords 不能为空")
            
        # 检查输出目录
        try:
            os.makedirs(self.config.output_dir, exist_ok=True)
        except Exception as e:
            issues.append(f"无法创建输出目录 {self.config.output_dir}: {e}")
            
        return issues
        
    def get_optimized_config_for_target(self, target_type: str = "boss") -> CrawlerConfig:
        """获取针对特定目标优化的配置"""
        optimized_config = CrawlerConfig(**asdict(self.config))
        
        if target_type == "boss":
            # BOSS直聘优化配置
            optimized_config.delay_min = 10.0
            optimized_config.delay_max = 20.0
            optimized_config.max_retries = 8
            optimized_config.handle_captcha = True
            optimized_config.max_captcha_retries = 5
            optimized_config.page_load_timeout = 45
            optimized_config.use_stealth_mode = True
            optimized_config.simulate_human_behavior = True
            
        return optimized_config
        
    def print_config_summary(self) -> None:
        """打印配置摘要"""
        print("\n" + "=" * 60)
        print("⚙️ 爬虫配置摘要")
        print("=" * 60)
        print(f"🎯 目标网站: {self.config.base_url}")
        print(f"🏙️ 目标城市: {', '.join(self.config.target_cities)}")
        print(f"🔍 搜索关键词: {', '.join(self.config.target_keywords)}")
        print(f"📄 每次搜索最大页数: {self.config.max_pages_per_search}")
        print(f"⏱️ 延迟范围: {self.config.delay_min}-{self.config.delay_max} 秒")
        print(f"🔄 最大重试次数: {self.config.max_retries}")
        print(f"🛡️ 隐蔽模式: {'启用' if self.config.use_stealth_mode else '禁用'}")
        print(f"🤖 人类行为模拟: {'启用' if self.config.simulate_human_behavior else '禁用'}")
        print(f"🔍 验证码处理: {'启用' if self.config.handle_captcha else '禁用'}")
        print(f"💾 输出目录: {self.config.output_dir}")
        print("=" * 60)

# 全局配置管理器实例
config_manager = ConfigManager()

# 便捷函数
def get_config() -> CrawlerConfig:
    """获取当前配置"""
    return config_manager.get_config()

def update_config(**kwargs) -> None:
    """更新配置"""
    config_manager.update_config(**kwargs)

def get_city_codes() -> Dict[str, str]:
    """获取城市代码"""
    return config_manager.get_city_codes()

def validate_config() -> List[str]:
    """验证配置"""
    return config_manager.validate_config()
