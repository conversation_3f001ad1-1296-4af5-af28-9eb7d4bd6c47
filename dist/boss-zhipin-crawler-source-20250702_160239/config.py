"""
BOSS直聘URL爬虫配置文件
"""

# 基础配置
BASE_URL = "https://www.zhipin.com"
SEARCH_URL = "https://www.zhipin.com/web/geek/job"

# 主要城市代码
CITY_CODES = {
    "北京": "101010100",
    "上海": "101020100",
    "深圳": "101280600",
    "杭州": "101210100",
    "广州": "101280100",
    "成都": "101270100",
    "南京": "101190100",
    "武汉": "101200100"
}

# 热门关键词
HIGH_PRIORITY_KEYWORDS = [
    "Python", "Java", "JavaScript", "前端", "后端",
    "产品经理", "UI设计师", "运营", "销售"
]

# 主要城市
MAJOR_CITIES = ["北京", "上海", "深圳", "杭州", "广州", "南京", "武汉", "成都"]

# User-Agent池
USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (<PERSON>HTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:121.0) Gecko/20100101 Firefox/121.0",
    "Mozilla/5.0 (X11; Linux x86_64; rv:121.0) Gecko/20100101 Firefox/121.0",
]

# Chrome浏览器选项
CHROME_OPTIONS = [
    "--no-sandbox",
    "--disable-dev-shm-usage",
    "--disable-blink-features=AutomationControlled",
    "--disable-extensions",
    "--disable-gpu"
]

# CSS选择器配置
SELECTORS = {
    "job_link": "a[href*='/job_detail/']",
    "job_link_alt": [
        "a[href*='job_detail']",
        ".job-list a[href*='job']",
        "a[ka='search_list_job']"
    ]
}
