#!/usr/bin/env python3
"""
高级反检测模块
基于Position_Crawler_1项目的核心反检测技术
实现浏览器指纹伪造、验证码处理等高级功能
"""

import time
import random
import json
import base64
import logging
from typing import Dict, List, Optional, Any

logger = logging.getLogger(__name__)

class AdvancedStealth:
    """高级反检测技术实现"""
    
    def __init__(self):
        self.fingerprint_config = self._generate_fingerprint_config()
        
    def _generate_fingerprint_config(self) -> Dict[str, Any]:
        """生成浏览器指纹配置"""
        return {
            # 屏幕分辨率
            "screen": {
                "width": random.choice([1920, 1366, 1536, 1440]),
                "height": random.choice([1080, 768, 864, 900]),
                "colorDepth": 24,
                "pixelDepth": 24
            },
            
            # 时区和语言
            "timezone": "Asia/Shanghai",
            "language": "zh-CN",
            "languages": ["zh-C<PERSON>", "zh", "en"],
            
            # 硬件信息
            "hardwareConcurrency": random.choice([4, 8, 12, 16]),
            "deviceMemory": random.choice([4, 8, 16, 32]),
            
            # WebGL指纹
            "webgl": {
                "vendor": "Google Inc. (Intel)",
                "renderer": "ANGLE (Intel, Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0, D3D11)"
            },
            
            # Canvas指纹
            "canvas": {
                "noise": random.uniform(0.0001, 0.001)
            }
        }
        
    def get_stealth_js_scripts(self) -> List[str]:
        """获取反检测JavaScript脚本"""
        scripts = []
        
        # 1. 隐藏webdriver属性
        scripts.append("""
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
        """)
        
        # 2. 修改Chrome对象
        scripts.append("""
            window.chrome = {
                runtime: {},
                loadTimes: function() {},
                csi: function() {},
                app: {}
            };
        """)
        
        # 3. 修改权限查询
        scripts.append("""
            const originalQuery = window.navigator.permissions.query;
            window.navigator.permissions.query = (parameters) => (
                parameters.name === 'notifications' ?
                Promise.resolve({ state: Notification.permission }) :
                originalQuery(parameters)
            );
        """)
        
        # 4. 修改插件信息
        scripts.append("""
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5],
            });
        """)
        
        # 5. 修改语言信息
        scripts.append(f"""
            Object.defineProperty(navigator, 'languages', {{
                get: () => {json.dumps(self.fingerprint_config['languages'])},
            }});
        """)
        
        # 6. Canvas指纹干扰
        scripts.append(f"""
            const getContext = HTMLCanvasElement.prototype.getContext;
            HTMLCanvasElement.prototype.getContext = function(type) {{
                if (type === '2d') {{
                    const context = getContext.apply(this, arguments);
                    const getImageData = context.getImageData;
                    context.getImageData = function(x, y, width, height) {{
                        const imageData = getImageData.apply(this, arguments);
                        const data = imageData.data;
                        for (let i = 0; i < data.length; i += 4) {{
                            data[i] += Math.floor(Math.random() * {self.fingerprint_config['canvas']['noise']} * 255);
                            data[i + 1] += Math.floor(Math.random() * {self.fingerprint_config['canvas']['noise']} * 255);
                            data[i + 2] += Math.floor(Math.random() * {self.fingerprint_config['canvas']['noise']} * 255);
                        }}
                        return imageData;
                    }};
                    return context;
                }}
                return getContext.apply(this, arguments);
            }};
        """)
        
        return scripts
        
    def get_advanced_chrome_options(self) -> List[str]:
        """获取高级Chrome选项"""
        options = [
            # 基础反检测
            "--no-sandbox",
            "--disable-dev-shm-usage",
            "--disable-blink-features=AutomationControlled",
            "--disable-extensions",
            "--disable-plugins",
            "--disable-default-apps",
            "--disable-sync",
            
            # 高级反检测
            "--disable-web-security",
            "--disable-features=VizDisplayCompositor",
            "--disable-background-timer-throttling",
            "--disable-backgrounding-occluded-windows",
            "--disable-renderer-backgrounding",
            "--disable-field-trial-config",
            "--disable-back-forward-cache",
            "--disable-background-networking",
            "--disable-component-update",
            "--disable-client-side-phishing-detection",
            "--disable-hang-monitor",
            "--disable-ipc-flooding-protection",
            "--disable-popup-blocking",
            "--disable-prompt-on-repost",
            "--disable-domain-reliability",
            "--disable-features=TranslateUI",
            "--disable-features=BlinkGenPropertyTrees",
            
            # 性能优化
            "--no-first-run",
            "--no-default-browser-check",
            "--disable-gpu",
            "--disable-software-rasterizer",
            "--disable-background-media-suspend",
            
            # 用户代理
            f"--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        ]
        
        return options
        
    def simulate_human_behavior(self, page) -> None:
        """模拟人类行为"""
        try:
            # 随机鼠标移动
            page.evaluate("""
                () => {
                    const moveCount = Math.floor(Math.random() * 5) + 3;
                    for (let i = 0; i < moveCount; i++) {
                        setTimeout(() => {
                            const x = Math.random() * window.innerWidth;
                            const y = Math.random() * window.innerHeight;
                            document.dispatchEvent(new MouseEvent('mousemove', {
                                clientX: x,
                                clientY: y,
                                bubbles: true
                            }));
                        }, i * 200 + Math.random() * 300);
                    }
                }
            """)
            
            # 随机滚动
            page.evaluate("""
                () => {
                    const scrollCount = Math.floor(Math.random() * 3) + 2;
                    for (let i = 0; i < scrollCount; i++) {
                        setTimeout(() => {
                            const scrollY = Math.random() * document.body.scrollHeight * 0.3;
                            window.scrollTo({
                                top: scrollY,
                                behavior: 'smooth'
                            });
                        }, i * 1000 + Math.random() * 500);
                    }
                }
            """)
            
            # 模拟键盘事件
            page.evaluate("""
                () => {
                    setTimeout(() => {
                        document.dispatchEvent(new KeyboardEvent('keydown', {
                            key: 'Tab',
                            code: 'Tab',
                            bubbles: true
                        }));
                    }, Math.random() * 2000);
                }
            """)
            
        except Exception as e:
            logger.debug(f"人类行为模拟异常: {e}")
            
    def get_random_delays(self) -> Dict[str, float]:
        """获取随机延迟配置"""
        return {
            "page_load": random.uniform(3, 8),
            "between_actions": random.uniform(1, 3),
            "scroll_delay": random.uniform(0.5, 2),
            "click_delay": random.uniform(0.3, 1.5),
            "type_delay": random.uniform(0.1, 0.5)
        }
        
    def handle_captcha_detection(self, page) -> bool:
        """检测和处理验证码"""
        try:
            # 检测常见的验证码元素
            captcha_selectors = [
                ".yidun",  # 易盾验证码
                ".geetest",  # 极验证码
                "#captcha",  # 通用验证码
                ".captcha",
                "[class*='captcha']",
                "[id*='captcha']",
                ".verification",
                "[class*='verification']"
            ]
            
            for selector in captcha_selectors:
                try:
                    element = page.query_selector(selector)
                    if element:
                        logger.warning(f"🔍 检测到验证码元素: {selector}")
                        
                        # 尝试处理滑块验证码
                        if "yidun" in selector or "geetest" in selector:
                            return self._handle_slider_captcha(page, selector)
                        
                        # 其他验证码类型
                        return self._handle_general_captcha(page, selector)
                        
                except Exception:
                    continue
                    
            return False
            
        except Exception as e:
            logger.error(f"验证码检测失败: {e}")
            return False
            
    def _handle_slider_captcha(self, page, selector: str) -> bool:
        """处理滑块验证码"""
        try:
            logger.info("🎯 尝试处理滑块验证码...")
            
            # 查找滑块元素
            slider_selectors = [
                f"{selector} .yidun_slider",
                f"{selector} .geetest_slider_button",
                f"{selector} [class*='slider']",
                f"{selector} [class*='drag']"
            ]
            
            for slider_sel in slider_selectors:
                try:
                    slider = page.query_selector(slider_sel)
                    if slider:
                        # 获取滑块位置
                        box = slider.bounding_box()
                        if box:
                            # 模拟人类滑动
                            start_x = box['x'] + box['width'] / 2
                            start_y = box['y'] + box['height'] / 2
                            
                            # 随机滑动距离
                            slide_distance = random.uniform(200, 300)
                            
                            # 执行滑动
                            page.mouse.move(start_x, start_y)
                            page.mouse.down()
                            
                            # 分段滑动，模拟人类行为
                            steps = random.randint(10, 20)
                            for i in range(steps):
                                progress = i / steps
                                current_x = start_x + slide_distance * progress
                                # 添加随机抖动
                                jitter_y = start_y + random.uniform(-2, 2)
                                page.mouse.move(current_x, jitter_y)
                                time.sleep(random.uniform(0.01, 0.05))
                                
                            page.mouse.up()
                            
                            # 等待验证结果
                            time.sleep(random.uniform(2, 4))
                            
                            logger.info("✅ 滑块验证码处理完成")
                            return True
                            
                except Exception:
                    continue
                    
            return False
            
        except Exception as e:
            logger.error(f"滑块验证码处理失败: {e}")
            return False
            
    def _handle_general_captcha(self, page, selector: str) -> bool:
        """处理一般验证码"""
        try:
            logger.info("🔍 检测到验证码，等待人工处理...")
            
            # 保存验证码截图
            timestamp = int(time.time())
            screenshot_path = f"captcha_{timestamp}.png"
            
            try:
                element = page.query_selector(selector)
                if element:
                    element.screenshot(path=screenshot_path)
                    logger.info(f"📸 验证码截图已保存: {screenshot_path}")
            except:
                page.screenshot(path=screenshot_path)
                logger.info(f"📸 页面截图已保存: {screenshot_path}")
            
            # 等待用户手动处理
            logger.info("⏳ 请手动处理验证码，程序将等待30秒...")
            time.sleep(30)
            
            return True
            
        except Exception as e:
            logger.error(f"验证码处理失败: {e}")
            return False
