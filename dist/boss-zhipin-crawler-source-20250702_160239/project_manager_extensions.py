#!/usr/bin/env python3
"""
项目管理器扩展功能
包含代理管理、性能监控等高级功能
"""

import os
import time
import json
import asyncio
import logging

logger = logging.getLogger(__name__)

class ProjectManagerExtensions:
    """项目管理器扩展功能"""
    
    def manage_proxy(self) -> None:
        """代理管理"""
        print("\n🌐 代理管理...")
        
        try:
            from proxy_manager import ProxyManager
            
            proxy_manager = ProxyManager()
            
            while True:
                print("\n代理管理选项:")
                print("1. 添加代理")
                print("2. 从文件加载代理")
                print("3. 检查代理")
                print("4. 查看代理状态")
                print("5. 生成代理报告")
                print("6. 重置失败代理")
                print("7. 返回主菜单")
                
                choice = input("请选择操作: ").strip()
                
                if choice == '1':
                    host = input("代理主机: ").strip()
                    port = input("代理端口: ").strip()
                    username = input("用户名(可选): ").strip() or None
                    password = input("密码(可选): ").strip() or None
                    
                    try:
                        proxy_manager.add_proxy(host, int(port), username, password)
                        print("✅ 代理已添加")
                    except ValueError:
                        print("❌ 端口必须是数字")
                        
                elif choice == '2':
                    filepath = input("代理文件路径: ").strip()
                    if os.path.exists(filepath):
                        count = proxy_manager.load_proxies_from_file(filepath)
                        print(f"✅ 加载了 {count} 个代理")
                    else:
                        print("❌ 文件不存在")
                        
                elif choice == '3':
                    if not proxy_manager.proxies:
                        print("❌ 没有代理可检查")
                        continue
                        
                    print("🔍 开始检查代理...")
                    
                    async def check_proxies():
                        results = await proxy_manager.check_all_proxies()
                        print(f"✅ 检查完成: {results}")
                        
                    asyncio.run(check_proxies())
                    
                elif choice == '4':
                    stats = proxy_manager.get_proxy_stats()
                    print(f"📊 代理统计: {stats}")
                    
                elif choice == '5':
                    report = proxy_manager.generate_report()
                    print(report)
                    
                    save_choice = input("是否保存报告？(y/n): ").lower()
                    if save_choice == 'y':
                        filename = proxy_manager.save_proxy_data()
                        if filename:
                            print(f"💾 报告已保存到: {filename}")
                            
                elif choice == '6':
                    count = proxy_manager.reset_failed_proxies()
                    print(f"🔄 重置了 {count} 个失败的代理")
                    
                elif choice == '7':
                    break
                    
                else:
                    print("❌ 无效的选择")
                    
        except ImportError:
            print("❌ 代理管理模块未找到")
        except Exception as e:
            print(f"❌ 代理管理失败: {e}")
            
    def monitor_performance(self) -> None:
        """性能监控"""
        print("\n📈 性能监控...")
        
        try:
            from performance_monitor import PerformanceMonitor
            
            print("性能监控选项:")
            print("1. 启动实时监控")
            print("2. 查看历史数据")
            print("3. 生成性能报告")
            print("4. 返回主菜单")
            
            choice = input("请选择操作: ").strip()
            
            if choice == '1':
                duration = input("监控时长(秒，默认60): ").strip()
                try:
                    duration = int(duration) if duration else 60
                except ValueError:
                    duration = 60
                    
                print(f"🔍 开始监控 {duration} 秒...")
                
                with PerformanceMonitor(interval=2.0) as monitor:
                    for i in range(duration // 5):
                        time.sleep(5)
                        status = monitor.get_current_status()
                        current = status.get("current_metrics", {})
                        print(f"CPU: {current.get('cpu_percent', 0):.1f}%, "
                              f"内存: {current.get('memory_used_mb', 0):.1f}MB")
                        
                    # 生成报告
                    report = monitor.generate_report()
                    print(report)
                    
                    # 保存数据
                    filename = monitor.save_metrics()
                    if filename:
                        print(f"💾 监控数据已保存到: {filename}")
                        
            elif choice == '2':
                # 查找监控数据文件
                import glob
                files = glob.glob("performance_metrics_*.json")
                if not files:
                    print("❌ 没有找到历史监控数据")
                    return
                    
                print("历史监控数据:")
                for i, file in enumerate(files, 1):
                    mtime = time.ctime(os.path.getmtime(file))
                    print(f"{i}. {file} ({mtime})")
                    
                try:
                    choice_idx = int(input("请选择文件编号: ")) - 1
                    if 0 <= choice_idx < len(files):
                        with open(files[choice_idx], 'r', encoding='utf-8') as f:
                            data = json.load(f)
                        
                        print(f"\n📊 监控数据摘要:")
                        metadata = data.get("metadata", {})
                        summary = data.get("summary", {})
                        
                        print(f"生成时间: {metadata.get('generated_at', 'N/A')}")
                        print(f"监控时长: {metadata.get('duration_seconds', 0):.1f}秒")
                        print(f"数据点数: {metadata.get('total_records', 0)}")
                        
                        if summary:
                            cpu = summary.get("cpu_usage", {})
                            memory = summary.get("memory_usage", {})
                            print(f"CPU平均: {cpu.get('avg', 0):.1f}%")
                            print(f"内存平均: {memory.get('avg_mb', 0):.1f}MB")
                            
                    else:
                        print("❌ 无效的选择")
                except ValueError:
                    print("❌ 请输入有效的数字")
                    
            elif choice == '3':
                print("📊 生成性能报告需要先启动监控...")
                
            elif choice == '4':
                return
                
            else:
                print("❌ 无效的选择")
                
        except ImportError:
            print("❌ 性能监控模块未找到")
        except Exception as e:
            print(f"❌ 性能监控失败: {e}")
            
    def advanced_analysis(self) -> None:
        """高级数据分析"""
        print("\n🔬 高级数据分析...")
        
        try:
            from data_analyzer import URLAnalyzer
            
            # 选择分析文件
            import glob
            files = glob.glob("output/*.txt") + glob.glob("*.txt")
            url_files = [f for f in files if 'url' in f.lower()]
            
            if not url_files:
                print("❌ 没有找到URL数据文件")
                return
                
            print("可用的数据文件:")
            for i, file in enumerate(url_files, 1):
                size = os.path.getsize(file) / 1024  # KB
                mtime = time.ctime(os.path.getmtime(file))
                print(f"{i}. {os.path.basename(file)} ({size:.1f}KB, {mtime})")
                
            try:
                choice = int(input("请选择文件编号: ")) - 1
                if 0 <= choice < len(url_files):
                    filepath = url_files[choice]
                else:
                    print("❌ 无效的选择")
                    return
            except ValueError:
                print("❌ 请输入有效的数字")
                return
                
            # 执行高级分析
            analyzer = URLAnalyzer()
            count = analyzer.load_urls_from_file(filepath)
            
            if count == 0:
                print("❌ 文件中没有有效数据")
                return
                
            print(f"📊 开始分析 {count} 个URL...")
            
            # 执行分析
            results = analyzer.analyze_urls()
            
            # 显示详细结果
            print("\n📈 分析结果:")
            
            # 基础统计
            basic = results.get('basic_stats', {})
            print(f"总URL数: {basic.get('total_urls', 0)}")
            print(f"唯一URL数: {basic.get('unique_urls', 0)}")
            print(f"重复率: {basic.get('duplicate_rate', 0):.2f}%")
            
            # URL模式
            patterns = results.get('url_patterns', {})
            print(f"job_detail类型: {patterns.get('job_detail', 0)}")
            print(f"HTTPS协议: {patterns.get('https_count', 0)}")
            
            # 质量评分
            quality = results.get('quality_check', {})
            print(f"质量评分: {quality.get('quality_score', 0):.1f}%")
            
            # 生成完整报告
            report = analyzer.generate_report()
            print(report)
            
            # 导出数据
            json_file = analyzer.export_analysis_json()
            if json_file:
                print(f"📊 分析数据已导出到: {json_file}")
                
            # 获取建议
            recommendations = analyzer.get_recommendations()
            print("\n💡 优化建议:")
            for i, rec in enumerate(recommendations, 1):
                print(f"   {i}. {rec}")
                
        except ImportError:
            print("❌ 数据分析模块未找到")
        except Exception as e:
            print(f"❌ 高级分析失败: {e}")
            
    def batch_validation(self) -> None:
        """批量URL验证"""
        print("\n🔍 批量URL验证...")
        
        try:
            from url_validator import URLValidator
            
            # 选择验证文件
            import glob
            files = glob.glob("output/*.txt") + glob.glob("*.txt")
            url_files = [f for f in files if 'url' in f.lower()]
            
            if not url_files:
                print("❌ 没有找到URL数据文件")
                return
                
            print("可用的数据文件:")
            for i, file in enumerate(url_files, 1):
                print(f"{i}. {os.path.basename(file)}")
                
            try:
                choice = int(input("请选择文件编号: ")) - 1
                if 0 <= choice < len(url_files):
                    filepath = url_files[choice]
                else:
                    print("❌ 无效的选择")
                    return
            except ValueError:
                print("❌ 请输入有效的数字")
                return
                
            # 读取URL
            with open(filepath, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                
            urls = [
                line.strip() 
                for line in lines 
                if line.strip() and not line.startswith('#')
            ]
            
            if not urls:
                print("❌ 文件中没有有效的URL")
                return
                
            print(f"📋 准备验证 {len(urls)} 个URL")
            
            # 配置验证参数
            max_concurrent = int(input("并发数(默认5): ") or "5")
            timeout = int(input("超时时间(默认10秒): ") or "10")
            
            print(f"🔍 开始验证，并发数: {max_concurrent}, 超时: {timeout}秒")
            
            # 执行验证
            async def run_validation():
                async with URLValidator(max_concurrent=max_concurrent, timeout=timeout) as validator:
                    results = await validator.validate_urls(urls)
                    
                    # 生成报告
                    report = validator.generate_report()
                    print(report)
                    
                    # 保存结果
                    result_file = validator.save_results()
                    if result_file:
                        print(f"💾 验证结果已保存到: {result_file}")
                        
                    # 保存有效URL
                    valid_urls = validator.get_valid_urls()
                    if valid_urls:
                        timestamp = time.strftime('%Y%m%d_%H%M%S')
                        valid_file = f"valid_urls_{timestamp}.txt"
                        with open(valid_file, 'w', encoding='utf-8') as f:
                            f.write(f"# 验证有效的URL - {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                            f.write(f"# 总数: {len(valid_urls)}\n\n")
                            for url in valid_urls:
                                f.write(url + '\n')
                        print(f"✅ 有效URL已保存到: {valid_file}")
                        
            asyncio.run(run_validation())
            
        except ImportError:
            print("❌ URL验证模块未找到")
        except Exception as e:
            print(f"❌ 批量验证失败: {e}")

# 将扩展功能混入到主项目管理器
def extend_project_manager(manager_class):
    """扩展项目管理器功能"""
    
    # 添加扩展方法
    extensions = ProjectManagerExtensions()
    manager_class.manage_proxy = extensions.manage_proxy
    manager_class.monitor_performance = extensions.monitor_performance
    manager_class.advanced_analysis = extensions.advanced_analysis
    manager_class.batch_validation = extensions.batch_validation
    
    return manager_class
