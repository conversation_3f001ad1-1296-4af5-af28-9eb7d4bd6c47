#!/usr/bin/env python3
"""
URL验证工具
验证爬取的URL是否有效和可访问
"""

import asyncio
import aiohttp
import time
import logging
from typing import List, Dict, Tuple
from urllib.parse import urlparse
import json

logger = logging.getLogger(__name__)

class URLValidator:
    """URL验证器"""
    
    def __init__(self, max_concurrent: int = 10, timeout: int = 10):
        self.max_concurrent = max_concurrent
        self.timeout = timeout
        self.session = None
        self.results = []
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        connector = aiohttp.TCPConnector(limit=self.max_concurrent)
        timeout = aiohttp.ClientTimeout(total=self.timeout)
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            }
        )
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
            
    async def validate_single_url(self, url: str) -> Dict[str, any]:
        """验证单个URL"""
        result = {
            'url': url,
            'status': 'unknown',
            'status_code': None,
            'response_time': None,
            'error': None,
            'redirect_url': None,
            'content_length': None,
            'content_type': None
        }
        
        start_time = time.time()
        
        try:
            async with self.session.get(url, allow_redirects=True) as response:
                result['status_code'] = response.status
                result['response_time'] = time.time() - start_time
                result['redirect_url'] = str(response.url) if str(response.url) != url else None
                result['content_length'] = response.headers.get('content-length')
                result['content_type'] = response.headers.get('content-type')
                
                if response.status == 200:
                    result['status'] = 'valid'
                elif response.status in [301, 302, 303, 307, 308]:
                    result['status'] = 'redirect'
                elif response.status == 404:
                    result['status'] = 'not_found'
                elif response.status in [403, 451]:
                    result['status'] = 'blocked'
                elif response.status >= 500:
                    result['status'] = 'server_error'
                else:
                    result['status'] = 'error'
                    
        except asyncio.TimeoutError:
            result['status'] = 'timeout'
            result['error'] = 'Request timeout'
            result['response_time'] = time.time() - start_time
            
        except aiohttp.ClientError as e:
            result['status'] = 'error'
            result['error'] = str(e)
            result['response_time'] = time.time() - start_time
            
        except Exception as e:
            result['status'] = 'error'
            result['error'] = f"Unexpected error: {str(e)}"
            result['response_time'] = time.time() - start_time
            
        return result
        
    async def validate_urls(self, urls: List[str]) -> List[Dict[str, any]]:
        """批量验证URL"""
        logger.info(f"🔍 开始验证 {len(urls)} 个URL...")
        
        # 创建信号量限制并发数
        semaphore = asyncio.Semaphore(self.max_concurrent)
        
        async def validate_with_semaphore(url):
            async with semaphore:
                return await self.validate_single_url(url)
                
        # 执行并发验证
        tasks = [validate_with_semaphore(url) for url in urls]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append({
                    'url': urls[i],
                    'status': 'error',
                    'error': str(result)
                })
            else:
                processed_results.append(result)
                
        self.results = processed_results
        logger.info(f"✅ URL验证完成")
        
        return processed_results
        
    def analyze_results(self) -> Dict[str, any]:
        """分析验证结果"""
        if not self.results:
            return {}
            
        total = len(self.results)
        status_counts = {}
        response_times = []
        
        for result in self.results:
            status = result.get('status', 'unknown')
            status_counts[status] = status_counts.get(status, 0) + 1
            
            if result.get('response_time'):
                response_times.append(result['response_time'])
                
        # 计算统计信息
        analysis = {
            'total_urls': total,
            'status_distribution': status_counts,
            'status_percentages': {
                status: (count / total * 100) 
                for status, count in status_counts.items()
            },
            'response_time_stats': {}
        }
        
        if response_times:
            analysis['response_time_stats'] = {
                'min': min(response_times),
                'max': max(response_times),
                'avg': sum(response_times) / len(response_times),
                'count': len(response_times)
            }
            
        return analysis
        
    def generate_report(self) -> str:
        """生成验证报告"""
        analysis = self.analyze_results()
        
        report_lines = []
        report_lines.append("=" * 80)
        report_lines.append("🔍 URL验证报告")
        report_lines.append("=" * 80)
        report_lines.append(f"验证时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append(f"总URL数量: {analysis.get('total_urls', 0)}")
        report_lines.append("")
        
        # 状态分布
        report_lines.append("📊 状态分布")
        report_lines.append("-" * 40)
        status_dist = analysis.get('status_distribution', {})
        status_pct = analysis.get('status_percentages', {})
        
        status_names = {
            'valid': '✅ 有效',
            'redirect': '🔄 重定向',
            'not_found': '❌ 未找到',
            'blocked': '🚫 被阻止',
            'server_error': '💥 服务器错误',
            'timeout': '⏰ 超时',
            'error': '❌ 错误',
            'unknown': '❓ 未知'
        }
        
        for status, count in status_dist.items():
            name = status_names.get(status, status)
            pct = status_pct.get(status, 0)
            report_lines.append(f"{name}: {count} ({pct:.1f}%)")
            
        # 响应时间统计
        rt_stats = analysis.get('response_time_stats', {})
        if rt_stats:
            report_lines.append("")
            report_lines.append("⏱️ 响应时间统计")
            report_lines.append("-" * 40)
            report_lines.append(f"最快: {rt_stats.get('min', 0):.2f}秒")
            report_lines.append(f"最慢: {rt_stats.get('max', 0):.2f}秒")
            report_lines.append(f"平均: {rt_stats.get('avg', 0):.2f}秒")
            report_lines.append(f"有效响应数: {rt_stats.get('count', 0)}")
            
        # 问题URL
        problem_urls = [
            result for result in self.results 
            if result.get('status') not in ['valid', 'redirect']
        ]
        
        if problem_urls:
            report_lines.append("")
            report_lines.append("⚠️ 问题URL (前10个)")
            report_lines.append("-" * 40)
            for result in problem_urls[:10]:
                url = result.get('url', '')[:60] + '...' if len(result.get('url', '')) > 60 else result.get('url', '')
                status = result.get('status', 'unknown')
                error = result.get('error', '')
                if error:
                    report_lines.append(f"{status}: {url} ({error})")
                else:
                    report_lines.append(f"{status}: {url}")
                    
        report_lines.append("")
        report_lines.append("=" * 80)
        
        return "\n".join(report_lines)
        
    def save_results(self, filename: str = None) -> str:
        """保存验证结果"""
        if not filename:
            timestamp = time.strftime('%Y%m%d_%H%M%S')
            filename = f"url_validation_{timestamp}.json"
            
        try:
            data = {
                'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
                'total_urls': len(self.results),
                'analysis': self.analyze_results(),
                'results': self.results
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
            logger.info(f"💾 验证结果已保存到: {filename}")
            return filename
            
        except Exception as e:
            logger.error(f"❌ 保存结果失败: {e}")
            return ""
            
    def get_valid_urls(self) -> List[str]:
        """获取有效的URL列表"""
        return [
            result['url'] for result in self.results 
            if result.get('status') == 'valid'
        ]
        
    def get_problem_urls(self) -> List[Dict[str, any]]:
        """获取有问题的URL"""
        return [
            result for result in self.results 
            if result.get('status') not in ['valid', 'redirect']
        ]

async def validate_url_file(filepath: str, output_file: str = None) -> None:
    """验证文件中的URL"""
    try:
        # 读取URL文件
        with open(filepath, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
        urls = [
            line.strip() 
            for line in lines 
            if line.strip() and not line.startswith('#')
        ]
        
        if not urls:
            print("❌ 文件中没有找到有效的URL")
            return
            
        print(f"📋 从文件中读取了 {len(urls)} 个URL")
        
        # 执行验证
        async with URLValidator(max_concurrent=5, timeout=15) as validator:
            results = await validator.validate_urls(urls)
            
            # 生成报告
            report = validator.generate_report()
            print(report)
            
            # 保存结果
            result_file = validator.save_results(output_file)
            
            # 保存有效URL
            valid_urls = validator.get_valid_urls()
            if valid_urls:
                timestamp = time.strftime('%Y%m%d_%H%M%S')
                valid_file = f"valid_urls_{timestamp}.txt"
                with open(valid_file, 'w', encoding='utf-8') as f:
                    f.write(f"# 验证有效的URL - {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write(f"# 总数: {len(valid_urls)}\n\n")
                    for url in valid_urls:
                        f.write(url + '\n')
                print(f"✅ 有效URL已保存到: {valid_file}")
                
    except Exception as e:
        print(f"❌ 验证过程失败: {e}")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        asyncio.run(validate_url_file(sys.argv[1]))
    else:
        print("使用方法: python url_validator.py <url_file_path>")
        print("示例: python url_validator.py output/position_crawler_urls_20250702_152000.txt")
